{"name": "regenerate-unicode-properties", "version": "10.2.0", "description": "Regenerate sets for Unicode properties and values.", "homepage": "https://github.com/mathiasbynens/regenerate-unicode-properties", "main": "index.js", "engines": {"node": ">=4"}, "files": ["LICENSE-MIT.txt", "index.js", "unicode-version.js", "Binary_Property", "General_Category", "Property_of_Strings", "<PERSON><PERSON><PERSON>", "Script_Extensions"], "keywords": ["unicode", "unicode-data", "regenerate"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/regenerate-unicode-properties.git"}, "bugs": "https://github.com/mathiasbynens/regenerate-unicode-properties/issues", "dependencies": {"regenerate": "^1.4.2"}, "devDependencies": {"@unicode/unicode-16.0.0": "^1.6.0", "ava": "^6.1.3", "fs-extra": "^11.2.0", "jsesc": "^3.0.2", "unicode-canonical-property-names-ecmascript": "^2.0.1"}, "scripts": {"build": "node build.js", "test": "ava tests/tests.js"}}