<div align="center">
    <a href="https://github.com/webpack/webpack-cli">
        <img width="200" height="200" src="https://webpack.js.org/assets/icon-square-big.svg">
    </a>
</div>

# webpack CLI

The official CLI of webpack

## About

webpack C<PERSON><PERSON> provides a flexible set of commands for developers to increase speed when setting up a custom webpack project. As of webpack v4, webpack is not expecting a configuration file, but often developers want to create a more custom webpack configuration based on their use-cases and needs. webpack CLI addresses these needs by providing a set of tools to improve the setup of custom webpack configuration.

## How to install

When you have followed the [Getting Started](https://webpack.js.org/guides/getting-started/) guide of webpack then webpack C<PERSON><PERSON> is already installed!

Otherwise

```bash
npm install --save-dev webpack-cli
```

or

```bash
yarn add webpack-cli --dev
```

## Supported arguments and commands

### Usage

All interactions with webpack-cli are of the form

```bash
npx webpack-cli [command] [options]
```

If no command is specified then `bundle` command is used by default

### Help Usage

To display basic commands and arguments -

```bash
npx webpack-cli --help
```

To display all supported commands and arguments -

```bash
npx webpack-cli --help=verbose
```

or

```bash
npx webpack-cli --help verbose
```

### Available Commands

```
  build|bundle|b [entries...] [options]                 Run webpack (default command, can be omitted).
  configtest|t [config-path]                            Validate a webpack configuration.
  help|h [command] [option]                             Display help for commands and options.
  info|i [options]                                      Outputs information about your system.
  init|create|new|c|n [generation-path] [options]       Initialize a new webpack project.
  loader|l [output-path] [options]                      Scaffold a loader.
  plugin|p [output-path] [options]                      Scaffold a plugin.
  serve|server|s [entries...] [options]                 Run the webpack dev server.
  version|v [commands...]                               Output the version number of 'webpack', 'webpack-cli' and 'webpack-dev-server' and commands.
  watch|w [entries...] [options]                        Run webpack and watch for files changes.
```

### Available Options

```
Options:
  -c, --config <value...>                          Provide path to a webpack configuration file e.g. ./webpack.config.js.
  --config-name <value...>                         Name of the configuration to use.
  -m, --merge                                      Merge two or more configurations using 'webpack-merge'.
  --disable-interpret                              Disable interpret for loading the config file.
  --env <value...>                                 Environment passed to the configuration when it is a function.
  --node-env <value>                               Sets process.env.NODE_ENV to the specified value.
  --define-process-env-node-env <value>            Sets process.env.NODE_ENV to the specified value. (Currently an alias for `--node-env`)
  --analyze                                        It invokes webpack-bundle-analyzer plugin to get bundle information.
  --progress [value]                               Print compilation progress during build.
  -j, --json [value]                               Prints result as JSON or store it in a file.
  --fail-on-warnings                               Stop webpack-cli process with non-zero exit code on warnings from webpack
  -d, --devtool <value>                            A developer tool to enhance debugging (false | eval | [inline-|hidden-|eval-][nosources-][cheap-[module-]]source-map).
  --no-devtool                                     Negative 'devtool' option.
  --entry <value...>                               A module that is loaded upon startup. Only the last one is exported.
  --mode <value>                                   Enable production optimizations or development hints.
  --name <value>                                   Name of the configuration. Used when loading multiple configurations.
  -o, --output-path <value>                        The output directory as **absolute path** (required).
  --stats [value]                                  Stats options object or preset name.
  --no-stats                                       Negative 'stats' option.
  -t, --target <value...>                          Environment to build for. Environment to build for. An array of environments to build for all of them when possible.
  --no-target                                      Negative 'target' option.
  -w, --watch                                      Enter watch mode, which rebuilds on file change.
  --no-watch                                       Negative 'watch' option.
  --watch-options-stdin                            Stop watching when stdin stream has ended.
  --no-watch-options-stdin                         Negative 'watch-options-stdin' option.

Global options:
  --color                                          Enable colors on console.
  --no-color                                       Disable colors on console.
  -v, --version                                    Output the version number of 'webpack', 'webpack-cli' and 'webpack-dev-server' and commands.
  -h, --help [verbose]                             Display help for commands and options.
```

Checkout [`OPTIONS.md`](https://github.com/webpack/webpack-cli/blob/master/OPTIONS.md) to see list of all available options.

## Exit codes and their meanings

| Exit Code | Description                                        |
| --------- | -------------------------------------------------- |
| `0`       | Success                                            |
| `1`       | Errors from webpack                                |
| `2`       | Configuration/options problem or an internal error |

## CLI Environment Variables

| Environment Variable                | Description                                                         |
| ----------------------------------- | ------------------------------------------------------------------- |
| `WEBPACK_CLI_SKIP_IMPORT_LOCAL`     | when `true` it will skip using the local instance of `webpack-cli`. |
| `WEBPACK_CLI_FORCE_LOAD_ESM_CONFIG` | when `true` it will force load the ESM config.                      |
| `WEBPACK_PACKAGE`                   | Use a custom webpack version in CLI.                                |
| `WEBPACK_DEV_SERVER_PACKAGE`        | Use a custom webpack-dev-server version in CLI.                     |
| `WEBPACK_CLI_HELP_WIDTH`            | Use custom width for help output.                                   |

## Configuration Environment Variables

You can use the following environment variables inside your webpack configuration:

| Environment Variable | Description                                  |
| -------------------- | -------------------------------------------- |
| `WEBPACK_SERVE`      | `true` if `serve\|s` is being used.          |
| `WEBPACK_BUILD`      | `true` if `build\|bundle\|b` is being used.  |
| `WEBPACK_WATCH`      | `true` if `--watch\|watch\|w` is being used. |

Checkout [webpack.js.org](https://webpack.js.org/api/cli/) for more detailed documentation of `webpack-cli`.
