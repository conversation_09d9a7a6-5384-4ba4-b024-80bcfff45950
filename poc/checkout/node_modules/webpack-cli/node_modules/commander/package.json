{"name": "commander", "version": "10.0.1", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser", "cli", "argument", "args", "argv"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"lint": "npm run lint:javascript && npm run lint:typescript", "lint:javascript": "eslint index.js esm.mjs \"lib/*.js\" \"tests/**/*.js\"", "lint:typescript": "eslint typings/*.ts tests/*.ts", "test": "jest && npm run test-typings", "test-esm": "node ./tests/esm-imports-test.mjs", "test-typings": "tsd", "typescript-checkJS": "tsc --allowJS --checkJS index.js lib/*.js --noEmit", "test-all": "npm run test && npm run lint && npm run typescript-checkJS && npm run test-esm"}, "files": ["index.js", "lib/*.js", "esm.mjs", "typings/index.d.ts", "package-support.json"], "type": "commonjs", "main": "./index.js", "exports": {".": {"types": "./typings/index.d.ts", "require": "./index.js", "import": "./esm.mjs"}, "./esm.mjs": "./esm.mjs"}, "devDependencies": {"@types/jest": "^29.2.4", "@types/node": "^18.11.18", "@typescript-eslint/eslint-plugin": "^5.47.1", "@typescript-eslint/parser": "^5.47.1", "eslint": "^8.30.0", "eslint-config-standard": "^17.0.0", "eslint-config-standard-with-typescript": "^24.0.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jest": "^27.1.7", "eslint-plugin-n": "^15.6.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.3.1", "ts-jest": "^29.0.3", "tsd": "^0.25.0", "typescript": "^4.9.4"}, "types": "typings/index.d.ts", "jest": {"testEnvironment": "node", "collectCoverage": true, "transform": {"^.+\\.tsx?$": "ts-jest"}, "testPathIgnorePatterns": ["/node_modules/"]}, "engines": {"node": ">=14"}, "support": true}