"use strict";(self.webpackChunkcheckout_microfrontend=self.webpackChunkcheckout_microfrontend||[]).push([[375],{56:(e,n,t)=>{e.exports=function(e){var n=t.nc;n&&e.setAttribute("nonce",n)}},72:e=>{var n=[];function t(e){for(var t=-1,r=0;r<n.length;r++)if(n[r].identifier===e){t=r;break}return t}function r(e,r){for(var o={},i=[],l=0;l<e.length;l++){var c=e[l],m=r.base?c[0]+r.base:c[0],s=o[m]||0,u="".concat(m," ").concat(s);o[m]=s+1;var d=t(u),p={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==d)n[d].references++,n[d].updater(p);else{var f=a(p,r);r.byIndex=l,n.splice(l,0,{identifier:u,updater:f,references:1})}i.push(u)}return i}function a(e,n){var t=n.domAPI(n);return t.update(e),function(n){if(n){if(n.css===e.css&&n.media===e.media&&n.sourceMap===e.sourceMap&&n.supports===e.supports&&n.layer===e.layer)return;t.update(e=n)}else t.remove()}}e.exports=function(e,a){var o=r(e=e||[],a=a||{});return function(e){e=e||[];for(var i=0;i<o.length;i++){var l=t(o[i]);n[l].references--}for(var c=r(e,a),m=0;m<o.length;m++){var s=t(o[m]);0===n[s].references&&(n[s].updater(),n.splice(s,1))}o=c}}},113:e=>{e.exports=function(e,n){if(n.styleSheet)n.styleSheet.cssText=e;else{for(;n.firstChild;)n.removeChild(n.firstChild);n.appendChild(document.createTextNode(e))}}},257:(e,n,t)=>{var r=t(72),a=t.n(r),o=t(825),i=t.n(o),l=t(659),c=t.n(l),m=t(56),s=t.n(m),u=t(540),d=t.n(u),p=t(113),f=t.n(p),b=t(278),y={};y.styleTagTransform=f(),y.setAttributes=s(),y.insert=c().bind(null,"head"),y.domAPI=i(),y.insertStyleElement=d(),a()(b.A,y),b.A&&b.A.locals&&b.A.locals},278:(e,n,t)=>{t.d(n,{A:()=>l});var r=t(601),a=t.n(r),o=t(314),i=t.n(o)()(a());i.push([e.id,".checkout-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n}\n\n.checkout-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.checkout-header h1 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 2.5rem;\n}\n\n.checkout-header p {\n  color: #666;\n  font-size: 1.1rem;\n}\n\n.checkout-content {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 40px;\n  align-items: start;\n}\n\n@media (max-width: 768px) {\n  .checkout-content {\n    grid-template-columns: 1fr;\n    gap: 30px;\n  }\n}\n\n/* Form Styles */\n.checkout-form {\n  background: #fff;\n  border-radius: 8px;\n  padding: 30px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.form-section {\n  margin-bottom: 30px;\n}\n\n.form-section h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 1.3rem;\n  border-bottom: 2px solid #f0f0f0;\n  padding-bottom: 10px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.form-row.three-col {\n  grid-template-columns: 1fr 1fr 1fr;\n}\n\n@media (max-width: 600px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 12px;\n  border: 2px solid #e1e1e1;\n  border-radius: 6px;\n  font-size: 16px;\n  transition: border-color 0.3s ease;\n  box-sizing: border-box;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n\n.place-order-btn {\n  width: 100%;\n  background: #007bff;\n  color: white;\n  border: none;\n  padding: 16px 24px;\n  font-size: 18px;\n  font-weight: 600;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n  margin-top: 20px;\n}\n\n.place-order-btn:hover {\n  background: #0056b3;\n}\n\n.place-order-btn:active {\n  transform: translateY(1px);\n}\n\n/* Order Summary Styles */\n.order-summary {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 30px;\n  height: fit-content;\n  position: sticky;\n  top: 20px;\n}\n\n.order-summary h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 1.3rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 10px;\n}\n\n.order-items {\n  margin-bottom: 20px;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.order-item:last-child {\n  border-bottom: none;\n}\n\n.item-details {\n  display: flex;\n  flex-direction: column;\n}\n\n.item-name {\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.item-quantity {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n.item-price {\n  font-weight: 600;\n  color: #333;\n}\n\n.order-totals {\n  border-top: 2px solid #e9ecef;\n  padding-top: 15px;\n}\n\n.total-line {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 10px;\n  color: #666;\n}\n\n.total-final {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #333;\n  border-top: 1px solid #e9ecef;\n  padding-top: 10px;\n  margin-top: 10px;\n}\n\n/* Web Component Specific Styles */\ncheckout-widget {\n  display: block;\n  width: 100%;\n}\n\n/* Loading and Error States */\n.loading {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n}\n\n.error {\n  background: #f8d7da;\n  color: #721c24;\n  padding: 15px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  border: 1px solid #f5c6cb;\n}\n",""]);const l=i},314:e=>{e.exports=function(e){var n=[];return n.toString=function(){return this.map(function(n){var t="",r=void 0!==n[5];return n[4]&&(t+="@supports (".concat(n[4],") {")),n[2]&&(t+="@media ".concat(n[2]," {")),r&&(t+="@layer".concat(n[5].length>0?" ".concat(n[5]):""," {")),t+=e(n),r&&(t+="}"),n[2]&&(t+="}"),n[4]&&(t+="}"),t}).join("")},n.i=function(e,t,r,a,o){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var l=0;l<this.length;l++){var c=this[l][0];null!=c&&(i[c]=!0)}for(var m=0;m<e.length;m++){var s=[].concat(e[m]);r&&i[s[0]]||(void 0!==o&&(void 0===s[5]||(s[1]="@layer".concat(s[5].length>0?" ".concat(s[5]):""," {").concat(s[1],"}")),s[5]=o),t&&(s[2]?(s[1]="@media ".concat(s[2]," {").concat(s[1],"}"),s[2]=t):s[2]=t),a&&(s[4]?(s[1]="@supports (".concat(s[4],") {").concat(s[1],"}"),s[4]=a):s[4]="".concat(a)),n.push(s))}},n}},375:(e,n,t)=>{t.r(n),t.d(n,{default:()=>u});var r=t(914),a=t.n(r);function o(e){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o(e)}function i(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable})),t.push.apply(t,r)}return t}function l(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?i(Object(t),!0).forEach(function(n){c(e,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):i(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})}return e}function c(e,n,t){return(n=function(e){var n=function(e){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var t=n.call(e,"string");if("object"!=o(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==o(n)?n:n+""}(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function m(e,n){return function(e){if(Array.isArray(e))return e}(e)||function(e,n){var t=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=t){var r,a,o,i,l=[],c=!0,m=!1;try{if(o=(t=t.call(e)).next,0===n){if(Object(t)!==t)return;c=!1}else for(;!(c=(r=o.call(t)).done)&&(l.push(r.value),l.length!==n);c=!0);}catch(e){m=!0,a=e}finally{try{if(!c&&null!=t.return&&(i=t.return(),Object(i)!==i))return}finally{if(m)throw a}}return l}}(e,n)||function(e,n){if(e){if("string"==typeof e)return s(e,n);var t={}.toString.call(e).slice(8,-1);return"Object"===t&&e.constructor&&(t=e.constructor.name),"Map"===t||"Set"===t?Array.from(e):"Arguments"===t||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t)?s(e,n):void 0}}(e,n)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,n){(null==n||n>e.length)&&(n=e.length);for(var t=0,r=Array(n);t<n;t++)r[t]=e[t];return r}t(257);const u=function(){var e=m((0,r.useState)({email:"",firstName:"",lastName:"",address:"",city:"",zipCode:"",country:"US",cardNumber:"",expiryDate:"",cvv:"",cardName:""}),2),n=e[0],t=e[1],o=m((0,r.useState)({items:[{id:1,name:"Premium Headphones",price:299.99,quantity:1},{id:2,name:"Wireless Mouse",price:79.99,quantity:2}],subtotal:459.97,tax:36.8,shipping:9.99,total:506.76}),1)[0],i=function(e){var n=e.target,r=n.name,a=n.value;t(function(e){return l(l({},e),{},c({},r,a))})};return a().createElement("div",{className:"checkout-container"},a().createElement("div",{className:"checkout-header"},a().createElement("h1",null,"Checkout"),a().createElement("p",null,"Complete your purchase")),a().createElement("div",{className:"checkout-content"},a().createElement("div",{className:"checkout-form"},a().createElement("form",{onSubmit:function(e){e.preventDefault(),alert("Order placed successfully! (This is a demo)"),console.log("Order data:",{formData:n,orderSummary:o})}},a().createElement("div",{className:"form-section"},a().createElement("h2",null,"Contact Information"),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"email"},"Email Address"),a().createElement("input",{type:"email",id:"email",name:"email",value:n.email,onChange:i,required:!0}))),a().createElement("div",{className:"form-section"},a().createElement("h2",null,"Shipping Address"),a().createElement("div",{className:"form-row"},a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"firstName"},"First Name"),a().createElement("input",{type:"text",id:"firstName",name:"firstName",value:n.firstName,onChange:i,required:!0})),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"lastName"},"Last Name"),a().createElement("input",{type:"text",id:"lastName",name:"lastName",value:n.lastName,onChange:i,required:!0}))),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"address"},"Address"),a().createElement("input",{type:"text",id:"address",name:"address",value:n.address,onChange:i,required:!0})),a().createElement("div",{className:"form-row"},a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"city"},"City"),a().createElement("input",{type:"text",id:"city",name:"city",value:n.city,onChange:i,required:!0})),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"zipCode"},"ZIP Code"),a().createElement("input",{type:"text",id:"zipCode",name:"zipCode",value:n.zipCode,onChange:i,required:!0})),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"country"},"Country"),a().createElement("select",{id:"country",name:"country",value:n.country,onChange:i,required:!0},a().createElement("option",{value:"US"},"United States"),a().createElement("option",{value:"CA"},"Canada"),a().createElement("option",{value:"UK"},"United Kingdom"),a().createElement("option",{value:"DE"},"Germany"),a().createElement("option",{value:"FR"},"France"))))),a().createElement("div",{className:"form-section"},a().createElement("h2",null,"Payment Information"),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"cardName"},"Name on Card"),a().createElement("input",{type:"text",id:"cardName",name:"cardName",value:n.cardName,onChange:i,required:!0})),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"cardNumber"},"Card Number"),a().createElement("input",{type:"text",id:"cardNumber",name:"cardNumber",value:n.cardNumber,onChange:i,placeholder:"1234 5678 9012 3456",required:!0})),a().createElement("div",{className:"form-row"},a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"expiryDate"},"Expiry Date"),a().createElement("input",{type:"text",id:"expiryDate",name:"expiryDate",value:n.expiryDate,onChange:i,placeholder:"MM/YY",required:!0})),a().createElement("div",{className:"form-group"},a().createElement("label",{htmlFor:"cvv"},"CVV"),a().createElement("input",{type:"text",id:"cvv",name:"cvv",value:n.cvv,onChange:i,placeholder:"123",required:!0})))),a().createElement("button",{type:"submit",className:"place-order-btn"},"Place Order - $",o.total.toFixed(2)))),a().createElement("div",{className:"order-summary"},a().createElement("h2",null,"Order Summary"),a().createElement("div",{className:"order-items"},o.items.map(function(e){return a().createElement("div",{key:e.id,className:"order-item"},a().createElement("div",{className:"item-details"},a().createElement("span",{className:"item-name"},e.name),a().createElement("span",{className:"item-quantity"},"Qty: ",e.quantity)),a().createElement("span",{className:"item-price"},"$",(e.price*e.quantity).toFixed(2)))})),a().createElement("div",{className:"order-totals"},a().createElement("div",{className:"total-line"},a().createElement("span",null,"Subtotal:"),a().createElement("span",null,"$",o.subtotal.toFixed(2))),a().createElement("div",{className:"total-line"},a().createElement("span",null,"Tax:"),a().createElement("span",null,"$",o.tax.toFixed(2))),a().createElement("div",{className:"total-line"},a().createElement("span",null,"Shipping:"),a().createElement("span",null,"$",o.shipping.toFixed(2))),a().createElement("div",{className:"total-line total-final"},a().createElement("span",null,"Total:"),a().createElement("span",null,"$",o.total.toFixed(2)))))))}},540:e=>{e.exports=function(e){var n=document.createElement("style");return e.setAttributes(n,e.attributes),e.insert(n,e.options),n}},601:e=>{e.exports=function(e){return e[1]}},659:e=>{var n={};e.exports=function(e,t){var r=function(e){if(void 0===n[e]){var t=document.querySelector(e);if(window.HTMLIFrameElement&&t instanceof window.HTMLIFrameElement)try{t=t.contentDocument.head}catch(e){t=null}n[e]=t}return n[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(t)}},825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var n=e.insertStyleElement(e);return{update:function(t){!function(e,n,t){var r="";t.supports&&(r+="@supports (".concat(t.supports,") {")),t.media&&(r+="@media ".concat(t.media," {"));var a=void 0!==t.layer;a&&(r+="@layer".concat(t.layer.length>0?" ".concat(t.layer):""," {")),r+=t.css,a&&(r+="}"),t.media&&(r+="}"),t.supports&&(r+="}");var o=t.sourceMap;o&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(o))))," */")),n.styleTagTransform(r,e,n.options)}(n,e,t)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)}}}}}]);