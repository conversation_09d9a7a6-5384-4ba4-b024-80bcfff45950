"use strict";(self.webpackChunkcheckout_microfrontend=self.webpackChunkcheckout_microfrontend||[]).push([[375,535],{56:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},72:e=>{var t=[];function n(e){for(var n=-1,r=0;r<t.length;r++)if(t[r].identifier===e){n=r;break}return n}function r(e,r){for(var a={},i=[],c=0;c<e.length;c++){var l=e[c],u=r.base?l[0]+r.base:l[0],s=a[u]||0,m="".concat(u," ").concat(s);a[u]=s+1;var p=n(m),d={css:l[1],media:l[2],sourceMap:l[3],supports:l[4],layer:l[5]};if(-1!==p)t[p].references++,t[p].updater(d);else{var f=o(d,r);r.byIndex=c,t.splice(c,0,{identifier:m,updater:f,references:1})}i.push(m)}return i}function o(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,o){var a=r(e=e||[],o=o||{});return function(e){e=e||[];for(var i=0;i<a.length;i++){var c=n(a[i]);t[c].references--}for(var l=r(e,o),u=0;u<a.length;u++){var s=n(a[u]);0===t[s].references&&(t[s].updater(),t.splice(s,1))}a=l}}},113:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}},257:(e,t,n)=>{var r=n(72),o=n.n(r),a=n(825),i=n.n(a),c=n(659),l=n.n(c),u=n(56),s=n.n(u),m=n(540),p=n.n(m),d=n(113),f=n.n(d),y=n(278),b={};b.styleTagTransform=f(),b.setAttributes=s(),b.insert=l().bind(null,"head"),b.domAPI=i(),b.insertStyleElement=p(),o()(y.A,b),y.A&&y.A.locals&&y.A.locals},278:(e,t,n)=>{n.d(t,{A:()=>c});var r=n(601),o=n.n(r),a=n(314),i=n.n(a)()(o());i.push([e.id,".checkout-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;\n}\n\n.checkout-header {\n  text-align: center;\n  margin-bottom: 40px;\n}\n\n.checkout-header h1 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 2.5rem;\n}\n\n.checkout-header p {\n  color: #666;\n  font-size: 1.1rem;\n}\n\n.checkout-content {\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: 40px;\n  align-items: start;\n}\n\n@media (max-width: 768px) {\n  .checkout-content {\n    grid-template-columns: 1fr;\n    gap: 30px;\n  }\n}\n\n/* Form Styles */\n.checkout-form {\n  background: #fff;\n  border-radius: 8px;\n  padding: 30px;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.form-section {\n  margin-bottom: 30px;\n}\n\n.form-section h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 1.3rem;\n  border-bottom: 2px solid #f0f0f0;\n  padding-bottom: 10px;\n}\n\n.form-group {\n  margin-bottom: 20px;\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 15px;\n}\n\n.form-row.three-col {\n  grid-template-columns: 1fr 1fr 1fr;\n}\n\n@media (max-width: 600px) {\n  .form-row {\n    grid-template-columns: 1fr;\n  }\n}\n\n.form-group label {\n  display: block;\n  margin-bottom: 5px;\n  color: #333;\n  font-weight: 500;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 12px;\n  border: 2px solid #e1e1e1;\n  border-radius: 6px;\n  font-size: 16px;\n  transition: border-color 0.3s ease;\n  box-sizing: border-box;\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);\n}\n\n.place-order-btn {\n  width: 100%;\n  background: #007bff;\n  color: white;\n  border: none;\n  padding: 16px 24px;\n  font-size: 18px;\n  font-weight: 600;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: background-color 0.3s ease;\n  margin-top: 20px;\n}\n\n.place-order-btn:hover {\n  background: #0056b3;\n}\n\n.place-order-btn:active {\n  transform: translateY(1px);\n}\n\n/* Order Summary Styles */\n.order-summary {\n  background: #f8f9fa;\n  border-radius: 8px;\n  padding: 30px;\n  height: fit-content;\n  position: sticky;\n  top: 20px;\n}\n\n.order-summary h2 {\n  color: #333;\n  margin-bottom: 20px;\n  font-size: 1.3rem;\n  border-bottom: 2px solid #e9ecef;\n  padding-bottom: 10px;\n}\n\n.order-items {\n  margin-bottom: 20px;\n}\n\n.order-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 0;\n  border-bottom: 1px solid #e9ecef;\n}\n\n.order-item:last-child {\n  border-bottom: none;\n}\n\n.item-details {\n  display: flex;\n  flex-direction: column;\n}\n\n.item-name {\n  font-weight: 500;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.item-quantity {\n  font-size: 0.9rem;\n  color: #666;\n}\n\n.item-price {\n  font-weight: 600;\n  color: #333;\n}\n\n.order-totals {\n  border-top: 2px solid #e9ecef;\n  padding-top: 15px;\n}\n\n.total-line {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 10px;\n  color: #666;\n}\n\n.total-final {\n  font-size: 1.2rem;\n  font-weight: 600;\n  color: #333;\n  border-top: 1px solid #e9ecef;\n  padding-top: 10px;\n  margin-top: 10px;\n}\n\n/* Web Component Specific Styles */\ncheckout-widget {\n  display: block;\n  width: 100%;\n}\n\n/* Loading and Error States */\n.loading {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n}\n\n.error {\n  background: #f8d7da;\n  color: #721c24;\n  padding: 15px;\n  border-radius: 6px;\n  margin-bottom: 20px;\n  border: 1px solid #f5c6cb;\n}\n",""]);const c=i},314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map(function(t){var n="",r=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),r&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),r&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n}).join("")},t.i=function(e,n,r,o,a){"string"==typeof e&&(e=[[null,e,void 0]]);var i={};if(r)for(var c=0;c<this.length;c++){var l=this[c][0];null!=l&&(i[l]=!0)}for(var u=0;u<e.length;u++){var s=[].concat(e[u]);r&&i[s[0]]||(void 0!==a&&(void 0===s[5]||(s[1]="@layer".concat(s[5].length>0?" ".concat(s[5]):""," {").concat(s[1],"}")),s[5]=a),n&&(s[2]?(s[1]="@media ".concat(s[2]," {").concat(s[1],"}"),s[2]=n):s[2]=n),o&&(s[4]?(s[1]="@supports (".concat(s[4],") {").concat(s[1],"}"),s[4]=o):s[4]="".concat(o)),t.push(s))}},t}},338:(e,t,n)=>{var r=n(672);t.H=r.createRoot,r.hydrateRoot},375:(e,t,n)=>{n.r(t),n.d(t,{default:()=>m});var r=n(914),o=n.n(r);function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function i(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?i(Object(n),!0).forEach(function(t){l(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):i(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function l(e,t,n){return(t=function(e){var t=function(e){if("object"!=a(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==a(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,a,i,c=[],l=!0,u=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;l=!1}else for(;!(l=(r=a.call(n)).done)&&(c.push(r.value),c.length!==t);l=!0);}catch(e){u=!0,o=e}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(u)throw o}}return c}}(e,t)||function(e,t){if(e){if("string"==typeof e)return s(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}n(257);const m=function(){var e=u((0,r.useState)({email:"",firstName:"",lastName:"",address:"",city:"",zipCode:"",country:"US",cardNumber:"",expiryDate:"",cvv:"",cardName:""}),2),t=e[0],n=e[1],a=u((0,r.useState)({items:[{id:1,name:"Premium Headphones",price:299.99,quantity:1},{id:2,name:"Wireless Mouse",price:79.99,quantity:2}],subtotal:459.97,tax:36.8,shipping:9.99,total:506.76}),1)[0],i=function(e){var t=e.target,r=t.name,o=t.value;n(function(e){return c(c({},e),{},l({},r,o))})};return o().createElement("div",{className:"checkout-container"},o().createElement("div",{className:"checkout-header"},o().createElement("h1",null,"Checkout"),o().createElement("p",null,"Complete your purchase")),o().createElement("div",{className:"checkout-content"},o().createElement("div",{className:"checkout-form"},o().createElement("form",{onSubmit:function(e){e.preventDefault(),alert("Order placed successfully! (This is a demo)"),console.log("Order data:",{formData:t,orderSummary:a})}},o().createElement("div",{className:"form-section"},o().createElement("h2",null,"Contact Information"),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"email"},"Email Address"),o().createElement("input",{type:"email",id:"email",name:"email",value:t.email,onChange:i,required:!0}))),o().createElement("div",{className:"form-section"},o().createElement("h2",null,"Shipping Address"),o().createElement("div",{className:"form-row"},o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"firstName"},"First Name"),o().createElement("input",{type:"text",id:"firstName",name:"firstName",value:t.firstName,onChange:i,required:!0})),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"lastName"},"Last Name"),o().createElement("input",{type:"text",id:"lastName",name:"lastName",value:t.lastName,onChange:i,required:!0}))),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"address"},"Address"),o().createElement("input",{type:"text",id:"address",name:"address",value:t.address,onChange:i,required:!0})),o().createElement("div",{className:"form-row"},o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"city"},"City"),o().createElement("input",{type:"text",id:"city",name:"city",value:t.city,onChange:i,required:!0})),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"zipCode"},"ZIP Code"),o().createElement("input",{type:"text",id:"zipCode",name:"zipCode",value:t.zipCode,onChange:i,required:!0})),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"country"},"Country"),o().createElement("select",{id:"country",name:"country",value:t.country,onChange:i,required:!0},o().createElement("option",{value:"US"},"United States"),o().createElement("option",{value:"CA"},"Canada"),o().createElement("option",{value:"UK"},"United Kingdom"),o().createElement("option",{value:"DE"},"Germany"),o().createElement("option",{value:"FR"},"France"))))),o().createElement("div",{className:"form-section"},o().createElement("h2",null,"Payment Information"),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"cardName"},"Name on Card"),o().createElement("input",{type:"text",id:"cardName",name:"cardName",value:t.cardName,onChange:i,required:!0})),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"cardNumber"},"Card Number"),o().createElement("input",{type:"text",id:"cardNumber",name:"cardNumber",value:t.cardNumber,onChange:i,placeholder:"1234 5678 9012 3456",required:!0})),o().createElement("div",{className:"form-row"},o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"expiryDate"},"Expiry Date"),o().createElement("input",{type:"text",id:"expiryDate",name:"expiryDate",value:t.expiryDate,onChange:i,placeholder:"MM/YY",required:!0})),o().createElement("div",{className:"form-group"},o().createElement("label",{htmlFor:"cvv"},"CVV"),o().createElement("input",{type:"text",id:"cvv",name:"cvv",value:t.cvv,onChange:i,placeholder:"123",required:!0})))),o().createElement("button",{type:"submit",className:"place-order-btn"},"Place Order - $",a.total.toFixed(2)))),o().createElement("div",{className:"order-summary"},o().createElement("h2",null,"Order Summary"),o().createElement("div",{className:"order-items"},a.items.map(function(e){return o().createElement("div",{key:e.id,className:"order-item"},o().createElement("div",{className:"item-details"},o().createElement("span",{className:"item-name"},e.name),o().createElement("span",{className:"item-quantity"},"Qty: ",e.quantity)),o().createElement("span",{className:"item-price"},"$",(e.price*e.quantity).toFixed(2)))})),o().createElement("div",{className:"order-totals"},o().createElement("div",{className:"total-line"},o().createElement("span",null,"Subtotal:"),o().createElement("span",null,"$",a.subtotal.toFixed(2))),o().createElement("div",{className:"total-line"},o().createElement("span",null,"Tax:"),o().createElement("span",null,"$",a.tax.toFixed(2))),o().createElement("div",{className:"total-line"},o().createElement("span",null,"Shipping:"),o().createElement("span",null,"$",a.shipping.toFixed(2))),o().createElement("div",{className:"total-line total-final"},o().createElement("span",null,"Total:"),o().createElement("span",null,"$",a.total.toFixed(2)))))))}},535:(e,t,n)=>{n.r(t),n.d(t,{default:()=>y});var r=n(914),o=n.n(r),a=n(338),i=n(375);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,u(r.key),r)}}function u(e){var t=function(e){if("object"!=c(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==c(t)?t:t+""}function s(e){var t="function"==typeof Map?new Map:void 0;return s=function(e){if(null===e||!function(e){try{return-1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return function(e,t,n){if(m())return Reflect.construct.apply(null,arguments);var r=[null];r.push.apply(r,t);var o=new(e.bind.apply(e,r));return n&&p(o,n.prototype),o}(e,arguments,d(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),p(n,e)},s(e)}function m(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(m=function(){return!!e})()}function p(e,t){return p=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},p(e,t)}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var f=function(e){function t(){var e;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),(e=function(e,t,n){return t=d(t),function(e,t){if(t&&("object"==c(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(e,m()?Reflect.construct(t,n||[],d(e).constructor):t.apply(e,n))}(this,t)).root=null,e.reactRoot=null,e}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&p(e,t)}(t,e),n=t,u=[{key:"observedAttributes",get:function(){return["theme","currency","locale"]}}],(r=[{key:"connectedCallback",value:function(){this.root=this;var e=document.createElement("div");e.style.width="100%",this.root.appendChild(e),this.reactRoot=(0,a.H)(e),this.renderReactApp()}},{key:"disconnectedCallback",value:function(){this.reactRoot&&this.reactRoot.unmount()}},{key:"renderReactApp",value:function(){if(this.reactRoot){var e={};this.reactRoot.render(o().createElement(i.default,e))}}},{key:"attributeChangedCallback",value:function(e,t,n){t!==n&&this.renderReactApp()}}])&&l(n.prototype,r),u&&l(n,u),Object.defineProperty(n,"prototype",{writable:!1}),n;var n,r,u}(s(HTMLElement));customElements.get("checkout-widget")||customElements.define("checkout-widget",f);const y=f},540:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},601:e=>{e.exports=function(e){return e[1]}},659:e=>{var t={};e.exports=function(e,n){var r=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!r)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");r.appendChild(n)}},825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var r="";n.supports&&(r+="@supports (".concat(n.supports,") {")),n.media&&(r+="@media ".concat(n.media," {"));var o=void 0!==n.layer;o&&(r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),r+=n.css,o&&(r+="}"),n.media&&(r+="}"),n.supports&&(r+="}");var a=n.sourceMap;a&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(a))))," */")),t.styleTagTransform(r,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}}}]);