{"name": "shell-app", "version": "1.0.0", "description": "Next.js shell application that consumes checkout microfrontend", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "export": "next export", "dev:with-checkout": "concurrently \"npm run dev\" \"cd ../checkout && npm start\"", "build:with-checkout": "cd ../checkout && npm run build && cd ../shell && npm run build"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "concurrently": "^8.2.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "typescript": "^5.0.0", "webpack": "^5.88.0"}, "keywords": ["nextjs", "microfrontend", "shell", "checkout"], "author": "", "license": "MIT"}