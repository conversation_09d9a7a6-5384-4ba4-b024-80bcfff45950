# Shell Application

A Next.js shell application that demonstrates microfrontend integration using webpack Module Federation. This app serves as the host/container that consumes remote microfrontends and renders them as native React components at build time.

## 🏗️ Architecture

This shell application acts as the main container that:

- Provides navigation and layout structure
- Dynamically loads microfrontends
- Handles routing between different sections
- Manages shared state and communication (if needed)

## 🚀 Quick Start

### Prerequisites

Make sure you have Node.js 18+ installed.

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

The application will be available at `http://localhost:3000`

### Running with Microfrontends

To see the full demo, you need to run both the shell and the checkout microfrontend:

**Option 1: Using the convenience script**
```bash
npm run dev:with-checkout
```

**Option 2: Manual setup**
```bash
# Terminal 1 - Start the checkout microfrontend
cd ../checkout
npm install
npm start

# Terminal 2 - Start the shell application
cd ../shell
npm install
npm run dev
```

## 📁 Project Structure

```
src/
├── components/
│   ├── Layout.tsx                 # Main layout component
│   └── FederatedCheckout.tsx      # Module Federation checkout wrapper
├── types/
│   └── remotes.d.ts              # TypeScript declarations for remote modules
├── pages/
│   ├── _app.tsx                   # Next.js app wrapper
│   ├── _document.tsx              # HTML document structure
│   ├── index.tsx                  # Home page
│   ├── checkout.tsx               # Checkout page (uses federated module)
│   └── about.tsx                  # About page
└── styles/
    └── globals.css                # Global styles

next.config.js                     # Next.js + Module Federation configuration
tsconfig.json                      # TypeScript configuration
package.json                       # Dependencies and scripts
.env.local.example                 # Environment configuration template
```

## 🔧 Key Components

### FederatedCheckout

A wrapper component that uses Module Federation to load the checkout component:

- Direct React component import from remote
- TypeScript support with proper typing
- Error handling and loading states
- Props passing to remote component

```tsx
<FederatedCheckout
  onOrderComplete={(data) => console.log('Order completed:', data)}
  onError={(error) => console.error('Checkout error:', error)}
  theme="default"
  currency="USD"
  locale="en-US"
/>
```

### Module Federation Configuration

The Next.js webpack configuration that enables remote module loading:

```javascript
// next.config.js
new ModuleFederationPlugin({
  name: 'shell',
  remotes: {
    checkout: 'checkout@http://localhost:3001/remoteEntry.js',
  },
  shared: {
    react: { singleton: true },
    'react-dom': { singleton: true },
  },
})
```

### TypeScript Declarations

Type definitions for remote modules ensure type safety:

```typescript
// src/types/remotes.d.ts
declare module 'checkout/CheckoutApp' {
  import { ComponentType } from 'react';
  interface CheckoutAppProps {
    theme?: string;
    currency?: string;
    locale?: string;
    onOrderComplete?: (orderData: any) => void;
  }
  const CheckoutApp: ComponentType<CheckoutAppProps>;
  export default CheckoutApp;
}
```

## 🌐 Pages

### Home Page (`/`)

- Overview of the microfrontend architecture
- Links to different sections
- Technical information and getting started guide

### Checkout Page (`/checkout`)

- Loads and renders the checkout component via Module Federation
- Demonstrates direct React component integration
- Includes error handling and loading states
- Shows debug information in development mode

### About Page (`/about`)

- Detailed architecture explanation
- Technical implementation details
- Benefits and use cases
- Learning resources

## ⚙️ Configuration

### Next.js Configuration

The `next.config.js` includes:

- Webpack configuration for microfrontend support
- CORS headers configuration
- Experimental features for better compatibility

### TypeScript Configuration

- Path aliases for cleaner imports
- Strict type checking
- Next.js specific settings

## 🔗 Module Federation Integration

### Integration Method

This application uses **webpack Module Federation** for seamless microfrontend integration:

- **Direct React Component Import**: Remote components are imported as native React components
- **Build-time Integration**: Components are resolved at build time for better performance
- **Type Safety**: Full TypeScript support with proper type declarations
- **Shared Dependencies**: React and React-DOM are shared between host and remotes

### Adding New Remote Modules

1. **Update webpack configuration** in `next.config.js`:

```javascript
new ModuleFederationPlugin({
  name: 'shell',
  remotes: {
    checkout: 'checkout@http://localhost:3001/remoteEntry.js',
    // Add new remote here
    products: 'products@http://localhost:3002/remoteEntry.js',
  },
  shared: {
    react: { singleton: true },
    'react-dom': { singleton: true },
  },
})
```

2. **Add TypeScript declarations** in `src/types/remotes.d.ts`:

```typescript
declare module 'products/ProductCatalog' {
  import { ComponentType } from 'react';
  interface ProductCatalogProps {
    category?: string;
    onProductSelect?: (product: any) => void;
  }
  const ProductCatalog: ComponentType<ProductCatalogProps>;
  export default ProductCatalog;
}
```

3. **Create a wrapper component** or use directly:

```tsx
import dynamic from 'next/dynamic';

const ProductCatalog = dynamic(() => import('products/ProductCatalog'), {
  ssr: false,
  loading: () => <div>Loading products...</div>
});
```

## 🛠️ Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Environment Variables

Create a `.env.local` file for environment-specific configuration:

```env
# Microfrontend URLs
NEXT_PUBLIC_CHECKOUT_URL=http://localhost:3001
NEXT_PUBLIC_ANOTHER_SERVICE_URL=http://localhost:3002
```

### Development Tips

1. **Hot Reloading**: Changes to the shell app will hot reload, but remote module changes require a page refresh
2. **Error Handling**: Check browser console for detailed error messages and Module Federation loading issues
3. **Network Tab**: Monitor network requests to see remote entry loading
4. **CORS Issues**: Make sure remote services serve with proper CORS headers
5. **Build Order**: Remote modules must be built and running before the shell can consume them

## 🚀 Production Deployment

### Build Process

```bash
npm run build
npm run start
```

### Deployment Considerations

1. **Microfrontend URLs**: Update URLs to production endpoints
2. **CORS Configuration**: Ensure proper CORS setup in production
3. **CDN**: Consider using CDN for microfrontend assets
4. **Monitoring**: Add error tracking and performance monitoring
5. **Fallbacks**: Implement proper fallback strategies

### Environment Configuration

```javascript
// next.config.js
const nextConfig = {
  env: {
    CHECKOUT_URL: process.env.CHECKOUT_URL || 'http://localhost:3001',
  },
};
```

## 🔍 Troubleshooting

### Common Issues

1. **Remote module not loading**
   - Check if the remote service is running and accessible
   - Verify the remoteEntry.js URL in browser network tab
   - Check CORS headers and Module Federation configuration

2. **TypeScript errors**
   - Ensure type declarations are properly defined in `src/types/remotes.d.ts`
   - Check that module names match the webpack configuration
   - Verify import paths are correct

3. **Build errors**
   - Make sure remote modules are built and running before building the shell
   - Check webpack configuration for syntax errors
   - Verify shared dependencies versions match

4. **Runtime errors**
   - Check browser console for Module Federation loading errors
   - Verify remote services are accessible at runtime
   - Check for version mismatches in shared dependencies

### Debug Mode

Set `NODE_ENV=development` to see debug information including:

- Module Federation loading status
- Remote service URLs
- Error messages and stack traces
- Performance metrics

## 📚 Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Webpack Module Federation](https://webpack.js.org/concepts/module-federation/)
- [Web Components](https://developer.mozilla.org/en-US/docs/Web/Web_Components)
- [Microfrontend Architecture](https://micro-frontends.org/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
