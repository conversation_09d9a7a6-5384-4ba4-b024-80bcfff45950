const { ModuleFederationPlugin } = require('webpack').container;

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,

  // Enable experimental features for better microfrontend support
  experimental: {
    esmExternals: false,
  },

  // Configure webpack for Module Federation
  webpack: (config, { isServer }) => {
    // Module Federation only works on client-side
    if (!isServer) {
      config.plugins.push(
        new ModuleFederationPlugin({
          name: 'shell',
          remotes: {
            checkout: `checkout@http://localhost:3001/remoteEntry.js`,
          },
          shared: {
            react: {
              singleton: true,
              requiredVersion: '^18.2.0',
            },
            'react-dom': {
              singleton: true,
              requiredVersion: '^18.2.0',
            },
          },
        })
      );

      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    return config;
  },
  
  // Configure headers for CORS if needed
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
        ],
      },
    ];
  },
  
  // Configure rewrites for API routes if needed
  async rewrites() {
    return [
      // Add any API rewrites here if needed
    ];
  },
};

module.exports = nextConfig;
