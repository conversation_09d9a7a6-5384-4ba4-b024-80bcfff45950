// Type declarations for Module Federation remotes

declare module 'checkout/CheckoutApp' {
  import { ComponentType } from 'react';
  
  interface CheckoutAppProps {
    // Add any props that the CheckoutApp component accepts
    theme?: string;
    currency?: string;
    locale?: string;
    onOrderComplete?: (orderData: any) => void;
    onError?: (error: string) => void;
  }
  
  const CheckoutApp: ComponentType<CheckoutAppProps>;
  export default CheckoutApp;
}

declare module 'checkout/CheckoutWebComponent' {
  import { ComponentType } from 'react';
  
  const CheckoutWebComponent: ComponentType<any>;
  export default CheckoutWebComponent;
}

// Global type for the checkout remote
declare global {
  interface Window {
    checkout?: {
      get: (module: string) => Promise<any>;
      init: (shared: any) => void;
    };
  }
}
