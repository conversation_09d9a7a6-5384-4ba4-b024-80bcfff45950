import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the checkout component from the remote
const CheckoutApp = dynamic(() => import('checkout/CheckoutApp'), {
  ssr: false, // Disable server-side rendering for federated modules
  loading: () => (
    <div className="microfrontend-loading">
      <div style={{ textAlign: 'center' }}>
        <h3>🛒 Loading Checkout...</h3>
        <p>Connecting to checkout microfrontend service</p>
        <div style={{ 
          marginTop: '1rem', 
          fontSize: '0.9rem', 
          color: '#666',
          fontFamily: 'monospace'
        }}>
          checkout@http://localhost:3001/remoteEntry.js
        </div>
      </div>
    </div>
  ),
});

interface FederatedCheckoutProps {
  onOrderComplete?: (orderData: any) => void;
  onError?: (error: string) => void;
  theme?: string;
  currency?: string;
  locale?: string;
}

const FederatedCheckout: React.FC<FederatedCheckoutProps> = ({
  onOrderComplete,
  onError,
  theme = 'default',
  currency = 'USD',
  locale = 'en-US',
}) => {
  const handleOrderComplete = (orderData: any) => {
    console.log('✅ Order completed:', orderData);
    if (onOrderComplete) {
      onOrderComplete(orderData);
    }
  };

  const handleError = (error: string) => {
    console.error('❌ Checkout error:', error);
    if (onError) {
      onError(error);
    }
  };

  return (
    <div className="federated-checkout-container">
      <Suspense
        fallback={
          <div className="microfrontend-loading">
            <div style={{ textAlign: 'center' }}>
              <h3>🛒 Initializing Checkout...</h3>
              <p>Loading federated module...</p>
            </div>
          </div>
        }
      >
        <CheckoutApp
          theme={theme}
          currency={currency}
          locale={locale}
          onOrderComplete={handleOrderComplete}
          onError={handleError}
        />
      </Suspense>
    </div>
  );
};

export default FederatedCheckout;
