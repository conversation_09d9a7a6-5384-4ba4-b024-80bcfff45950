import React, { useState } from 'react';
import Layout from '@/components/Layout';
import FederatedCheckout from '@/components/FederatedCheckout';

const CheckoutPage: React.FC = () => {
  const [orderStatus, setOrderStatus] = useState<'idle' | 'processing' | 'completed' | 'error'>('idle');
  const [orderData, setOrderData] = useState<any>(null);
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleOrderComplete = (data: any) => {
    console.log('✅ Order completed successfully:', data);
    setOrderStatus('completed');
    setOrderData(data);
  };

  const handleCheckoutError = (error: string) => {
    console.error('❌ Checkout error:', error);
    setOrderStatus('error');
    setErrorMessage(error);
  };



  return (
    <Layout>
      <div className="page-header">
        <h1 className="page-title">Checkout</h1>
        <p className="page-subtitle">
          Complete your purchase using our Module Federation checkout experience
        </p>

        {/* Status indicator */}
        <div style={{ marginTop: '1rem' }}>
          {orderStatus === 'idle' && (
            <div className="loading" style={{ justifyContent: 'center' }}>
              Ready to checkout...
            </div>
          )}
          {orderStatus === 'processing' && (
            <div className="loading" style={{ justifyContent: 'center' }}>
              Processing your order...
            </div>
          )}
          {orderStatus === 'completed' && (
            <div className="success">
              ✅ Order completed successfully!
            </div>
          )}
          {orderStatus === 'error' && (
            <div className="error">
              ❌ Checkout error: {errorMessage}
            </div>
          )}
        </div>
      </div>

      {/* Information card */}
      <div className="card" style={{ marginBottom: '2rem' }}>
        <h2>🔧 Module Federation Integration</h2>
        <p>
          This checkout experience is powered by a separate React application loaded through webpack Module Federation.
          The component is imported directly at build time and rendered as a native React component.
        </p>

        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
          gap: '1rem',
          marginTop: '1rem',
          fontSize: '0.9rem'
        }}>
          <div>
            <strong>🏗️ Architecture:</strong><br />
            Module Federation (Direct Import)
          </div>
          <div>
            <strong>🌐 Remote URL:</strong><br />
            <code>localhost:3001/remoteEntry.js</code>
          </div>
          <div>
            <strong>⚛️ Technology:</strong><br />
            React 18 + Webpack 5
          </div>
          <div>
            <strong>📦 Module:</strong><br />
            <code>checkout/CheckoutApp</code>
          </div>
        </div>
      </div>

      {/* Order completion status */}
      {orderStatus === 'completed' && orderData && (
        <div className="card" style={{ marginBottom: '2rem', background: '#d4edda' }}>
          <h3>🎉 Order Completed!</h3>
          <p>Thank you for your purchase. Your order has been processed successfully.</p>
          <div style={{
            background: '#fff',
            padding: '1rem',
            borderRadius: '4px',
            marginTop: '1rem',
            fontFamily: 'monospace',
            fontSize: '0.9rem'
          }}>
            <strong>Order Data:</strong><br />
            {JSON.stringify(orderData, null, 2)}
          </div>
        </div>
      )}

      {/* Federated checkout component */}
      <div className="card">
        <FederatedCheckout
          onOrderComplete={handleOrderComplete}
          onError={handleCheckoutError}
          theme="default"
          currency="USD"
          locale="en-US"
        />
      </div>

      {/* Debug information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="card" style={{ marginTop: '2rem', background: '#f8f9fa' }}>
          <h3>🐛 Debug Information</h3>
          <div style={{ fontFamily: 'monospace', fontSize: '0.9rem' }}>
            <div><strong>Order Status:</strong> {orderStatus}</div>
            <div><strong>Remote Module:</strong> checkout/CheckoutApp</div>
            <div><strong>Remote URL:</strong> http://localhost:3001/remoteEntry.js</div>
            <div><strong>Integration Type:</strong> Module Federation (Direct Import)</div>
            {errorMessage && <div><strong>Error:</strong> {errorMessage}</div>}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default CheckoutPage;
