import React, { useState } from 'react';
import Layout from '@/components/Layout';
import MicrofrontendWrapper from '@/components/MicrofrontendWrapper';
import { MICROFRONTEND_CONFIGS } from '@/hooks/useMicrofrontend';

const CheckoutPage: React.FC = () => {
  const [loadStatus, setLoadStatus] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  const handleMicrofrontendLoad = () => {
    console.log('✅ Checkout microfrontend loaded successfully');
    setLoadStatus('loaded');
  };

  const handleMicrofrontendError = (error: string) => {
    console.error('❌ Checkout microfrontend failed to load:', error);
    setLoadStatus('error');
    setErrorMessage(error);
  };

  const customLoadingFallback = (
    <div className="microfrontend-loading">
      <div style={{ textAlign: 'center' }}>
        <h3>🛒 Loading Checkout...</h3>
        <p>Connecting to checkout microfrontend service</p>
        <div style={{ 
          marginTop: '1rem', 
          fontSize: '0.9rem', 
          color: '#666',
          fontFamily: 'monospace'
        }}>
          {MICROFRONTEND_CONFIGS.checkout.url}
        </div>
      </div>
    </div>
  );

  const customErrorFallback = (
    <div className="microfrontend-error">
      <h3>⚠️ Checkout Service Unavailable</h3>
      <p>We're having trouble loading the checkout experience.</p>
      <div style={{ 
        background: '#fff', 
        padding: '1rem', 
        borderRadius: '4px', 
        margin: '1rem 0',
        border: '1px solid #f56565'
      }}>
        <strong>Error:</strong> {errorMessage}
      </div>
      <div style={{ marginBottom: '1rem' }}>
        <strong>Troubleshooting:</strong>
        <ul style={{ textAlign: 'left', marginTop: '0.5rem' }}>
          <li>Make sure the checkout service is running on port 3001</li>
          <li>Run: <code>cd poc/checkout && npm start</code></li>
          <li>Check that <code>http://localhost:3001</code> is accessible</li>
        </ul>
      </div>
      <button 
        className="btn"
        onClick={() => window.location.reload()}
        style={{ background: '#007bff', color: 'white' }}
      >
        🔄 Retry Loading
      </button>
    </div>
  );

  return (
    <Layout>
      {/* Microfrontend container */}
      <div className="card">
        <MicrofrontendWrapper
          name="checkout"
          url={MICROFRONTEND_CONFIGS.checkout.url}
          elementName={MICROFRONTEND_CONFIGS.checkout.elementName}
          onLoad={handleMicrofrontendLoad}
          onError={handleMicrofrontendError}
          fallback={customLoadingFallback}
          errorFallback={customErrorFallback}
          // You can pass additional props that will become attributes on the web component
          // theme="default"
          // currency="USD"
          // locale="en-US"
        />
      </div>

      {/* Debug information */}
      {process.env.NODE_ENV === 'development' && (
        <div className="card" style={{ marginTop: '2rem', background: '#f8f9fa' }}>
          <h3>🐛 Debug Information</h3>
          <div style={{ fontFamily: 'monospace', fontSize: '0.9rem' }}>
            <div><strong>Load Status:</strong> {loadStatus}</div>
            <div><strong>Service URL:</strong> {MICROFRONTEND_CONFIGS.checkout.url}</div>
            <div><strong>Element Name:</strong> {MICROFRONTEND_CONFIGS.checkout.elementName}</div>
            {errorMessage && <div><strong>Error:</strong> {errorMessage}</div>}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default CheckoutPage;
