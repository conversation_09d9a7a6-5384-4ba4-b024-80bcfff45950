/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/checkout"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/checkout\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/checkout.tsx */ \"./src/pages/checkout.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/checkout\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZob21lJTJGanVsaWFuJTJGcHJvamVjdHMlMkZwcml2YXRlJTJGYXJjaC1wcm9wb3NhbC1pYm0lMkZwb2MlMkZzaGVsbCUyRnNyYyUyRnBhZ2VzJTJGY2hlY2tvdXQudHN4JnBhZ2U9JTJGY2hlY2tvdXQhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jOTgwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvY2hlY2tvdXRcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9jaGVja291dC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2NoZWNrb3V0XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlhQSxxQkFBbUI7ZUFBbkJBOztJQUpBQyxpQkFBZTtlQUFmQTs7SUFHQUMsaUJBQWU7ZUFBZkE7O0lBSkFDLGdCQUFjO2VBQWRBOztJQUVBQyxnQkFBYztlQUFkQTs7SUFJQUMsc0JBQW9CO2VBQXBCQTs7SUFIQUMscUJBQW1CO2VBQW5CQTs7Ozs7Ozs7SUF1UUdDLFlBQVU7ZUFBVkE7OztBQTFRVCxNQUFNSixpQkFBaUI7QUFDdkIsTUFBTUYsa0JBQWtCO0FBQ3hCLE1BQU1HLGlCQUFpQjtBQUN2QixNQUFNRSxzQkFBc0I7QUFDNUIsTUFBTUosa0JBQWtCO0FBQ3hCLE1BQU1GLHNCQUFzQjtBQUM1QixNQUFNSyx1QkFBdUI7O1VBdUl4QkcsWUFBQUE7Ozs7R0FBQUEsZ0JBQUFBLENBQUFBLGVBQUFBLENBQUFBLENBQUFBOztVQThEQUMsd0JBQUFBOzs7OztHQUFBQSw0QkFBQUEsQ0FBQUEsMkJBQUFBLENBQUFBLENBQUFBO0FBK0RMLFNBQVNGLFdBQVdHLEtBQVU7SUFDbkMsNkVBQTZFO0lBQzdFLDZFQUE2RTtJQUM3RSx1RUFBdUU7SUFDdkUsZ0JBQWdCO0lBQ2hCLE9BQ0VBLFNBQ0MsUUFBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsZUFDL0MsT0FBT0EsTUFBTUMsSUFBSSxLQUFLO0FBRTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMudHM/ZWYxYyJdLCJuYW1lcyI6WyJBQ1RJT05fRkFTVF9SRUZSRVNIIiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1BSRUZFVENIIiwiQUNUSU9OX1JFRlJFU0giLCJBQ1RJT05fUkVTVE9SRSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiQUNUSU9OX1NFUlZFUl9QQVRDSCIsImlzVGhlbmFibGUiLCJQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJ2YWx1ZSIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/dynamic.js":
/*!******************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/dynamic.js ***!
  \******************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    /**\n * This function lets you dynamically import a component.\n * It uses [React.lazy()](https://react.dev/reference/react/lazy) with [Suspense](https://react.dev/reference/react/Suspense) under the hood.\n *\n * Read more: [Next.js Docs: `next/dynamic`](https://nextjs.org/docs/app/building-your-application/optimizing/lazy-loading#nextdynamic)\n */ default: function() {\n        return dynamic;\n    },\n    noSSR: function() {\n        return noSSR;\n    }\n});\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\"));\nconst _loadablesharedruntime = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! ./loadable.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\"));\nconst isServerSide = \"object\" === \"undefined\";\n// Normalize loader to return the module as form { default: Component } for `React.lazy`.\n// Also for backward compatible since next/dynamic allows to resolve a component directly with loader\n// Client component reference proxy need to be converted to a module.\nfunction convertModule(mod) {\n    return {\n        default: (mod == null ? void 0 : mod.default) || mod\n    };\n}\nfunction noSSR(LoadableInitializer, loadableOptions) {\n    // Removing webpack and modules means react-loadable won't try preloading\n    delete loadableOptions.webpack;\n    delete loadableOptions.modules;\n    // This check is necessary to prevent react-loadable from initializing on the server\n    if (!isServerSide) {\n        return LoadableInitializer(loadableOptions);\n    }\n    const Loading = loadableOptions.loading;\n    // This will only be rendered on the server side\n    return ()=>/*#__PURE__*/ (0, _jsxruntime.jsx)(Loading, {\n            error: null,\n            isLoading: true,\n            pastDelay: false,\n            timedOut: false\n        });\n}\nfunction dynamic(dynamicOptions, options) {\n    let loadableFn = _loadablesharedruntime.default;\n    let loadableOptions = {\n        // A loading component is not required, so we default it\n        loading: (param)=>{\n            let { error, isLoading, pastDelay } = param;\n            if (!pastDelay) return null;\n            if (true) {\n                if (isLoading) {\n                    return null;\n                }\n                if (error) {\n                    return /*#__PURE__*/ (0, _jsxruntime.jsxs)(\"p\", {\n                        children: [\n                            error.message,\n                            /*#__PURE__*/ (0, _jsxruntime.jsx)(\"br\", {}),\n                            error.stack\n                        ]\n                    });\n                }\n            }\n            return null;\n        }\n    };\n    // Support for direct import(), eg: dynamic(import('../hello-world'))\n    // Note that this is only kept for the edge case where someone is passing in a promise as first argument\n    // The react-loadable babel plugin will turn dynamic(import('../hello-world')) into dynamic(() => import('../hello-world'))\n    // To make sure we don't execute the import without rendering first\n    if (dynamicOptions instanceof Promise) {\n        loadableOptions.loader = ()=>dynamicOptions;\n    // Support for having import as a function, eg: dynamic(() => import('../hello-world'))\n    } else if (typeof dynamicOptions === \"function\") {\n        loadableOptions.loader = dynamicOptions;\n    // Support for having first argument being options, eg: dynamic({loader: import('../hello-world')})\n    } else if (typeof dynamicOptions === \"object\") {\n        loadableOptions = {\n            ...loadableOptions,\n            ...dynamicOptions\n        };\n    }\n    // Support for passing options, eg: dynamic(import('../hello-world'), {loading: () => <p>Loading something</p>})\n    loadableOptions = {\n        ...loadableOptions,\n        ...options\n    };\n    const loaderFn = loadableOptions.loader;\n    const loader = ()=>loaderFn != null ? loaderFn().then(convertModule) : Promise.resolve(convertModule(()=>null));\n    // coming from build/babel/plugins/react-loadable-plugin.js\n    if (loadableOptions.loadableGenerated) {\n        loadableOptions = {\n            ...loadableOptions,\n            ...loadableOptions.loadableGenerated\n        };\n        delete loadableOptions.loadableGenerated;\n    }\n    // support for disabling server side rendering, eg: dynamic(() => import('../hello-world'), {ssr: false}).\n    if (typeof loadableOptions.ssr === \"boolean\" && !loadableOptions.ssr) {\n        delete loadableOptions.webpack;\n        delete loadableOptions.modules;\n        return noSSR(loadableFn, loadableOptions);\n    }\n    return loadableFn({\n        ...loadableOptions,\n        loader: loader\n    });\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=dynamic.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/dynamic.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js":
/*!******************************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js ***!
  \******************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"LoadableContext\", ({\n    enumerable: true,\n    get: function() {\n        return LoadableContext;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\"));\nconst LoadableContext = _react.default.createContext(null);\nif (true) {\n    LoadableContext.displayName = \"LoadableContext\";\n} //# sourceMappingURL=loadable-context.shared-runtime.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQU1PLE1BQU1BLFNBQUFBLFdBQWtCQyxHQUFBQSx5QkFBc0NDLENBQUEsQ0FBQUMsbUJBQUFBLENBQUE7QUFFckUsTUFBSUMsa0JBQW9CQyxPQUFLQyxPQUFBLENBQUFDLGFBQWM7SUFDekNQLElBQWdCUSxFQUFjO0lBQ2hDUixnQkFBQVEsV0FBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL3NoYXJlZC9saWIvbG9hZGFibGUtY29udGV4dC5zaGFyZWQtcnVudGltZS50cz80YTk5Il0sIm5hbWVzIjpbIkxvYWRhYmxlQ29udGV4dCIsIlJlYWN0IiwiXyIsInJlcXVpcmUiLCJwcm9jZXNzIiwiX3JlYWN0IiwiZGVmYXVsdCIsImNyZWF0ZUNvbnRleHQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/shared/lib/loadable.shared-runtime.js":
/*!**********************************************************************!*\
  !*** ./node_modules/next/dist/shared/lib/loadable.shared-runtime.js ***!
  \**********************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("// TODO: Remove use of `any` type.\n/**\n@copyright (c) 2017-present James Kyle <<EMAIL>>\n MIT License\n Permission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n The above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND\nNONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE\nLIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION\nOF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION\nWITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE\n*/ // https://github.com/jamiebuilds/react-loadable/blob/v5.5.0/src/index.js\n// Modified to be compatible with webpack 4 / Next.js\n\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\"));\nconst _loadablecontextsharedruntime = __webpack_require__(/*! ./loadable-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/loadable-context.shared-runtime.js\");\nfunction resolve(obj) {\n    return obj && obj.default ? obj.default : obj;\n}\nconst ALL_INITIALIZERS = [];\nconst READY_INITIALIZERS = [];\nlet initialized = false;\nfunction load(loader) {\n    let promise = loader();\n    let state = {\n        loading: true,\n        loaded: null,\n        error: null\n    };\n    state.promise = promise.then((loaded)=>{\n        state.loading = false;\n        state.loaded = loaded;\n        return loaded;\n    }).catch((err)=>{\n        state.loading = false;\n        state.error = err;\n        throw err;\n    });\n    return state;\n}\nfunction createLoadableComponent(loadFn, options) {\n    var _s = $RefreshSig$(), _s1 = $RefreshSig$();\n    let opts = Object.assign({\n        loader: null,\n        loading: null,\n        delay: 200,\n        timeout: null,\n        webpack: null,\n        modules: null\n    }, options);\n    /** @type LoadableSubscription */ let subscription = null;\n    function init() {\n        if (!subscription) {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            const sub = new LoadableSubscription(loadFn, opts);\n            subscription = {\n                getCurrentValue: sub.getCurrentValue.bind(sub),\n                subscribe: sub.subscribe.bind(sub),\n                retry: sub.retry.bind(sub),\n                promise: sub.promise.bind(sub)\n            };\n        }\n        return subscription.promise();\n    }\n    // Server only\n    if (false) {}\n    // Client only\n    if (!initialized && \"object\" !== \"undefined\") {\n        // require.resolveWeak check is needed for environments that don't have it available like Jest\n        const moduleIds = opts.webpack && \"function\" === \"function\" ? opts.webpack() : opts.modules;\n        if (moduleIds) {\n            READY_INITIALIZERS.push((ids)=>{\n                for (const moduleId of moduleIds){\n                    if (ids.includes(moduleId)) {\n                        return init();\n                    }\n                }\n            });\n        }\n    }\n    function useLoadableModule() {\n        _s();\n        init();\n        const context = _react.default.useContext(_loadablecontextsharedruntime.LoadableContext);\n        if (context && Array.isArray(opts.modules)) {\n            opts.modules.forEach((moduleName)=>{\n                context(moduleName);\n            });\n        }\n    }\n    _s(useLoadableModule, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n    function LoadableComponent(props, ref) {\n        _s1();\n        useLoadableModule();\n        const state = _react.default.useSyncExternalStore(subscription.subscribe, subscription.getCurrentValue, subscription.getCurrentValue);\n        _react.default.useImperativeHandle(ref, ()=>({\n                retry: subscription.retry\n            }), []);\n        return _react.default.useMemo(()=>{\n            if (state.loading || state.error) {\n                return /*#__PURE__*/ _react.default.createElement(opts.loading, {\n                    isLoading: state.loading,\n                    pastDelay: state.pastDelay,\n                    timedOut: state.timedOut,\n                    error: state.error,\n                    retry: subscription.retry\n                });\n            } else if (state.loaded) {\n                return /*#__PURE__*/ _react.default.createElement(resolve(state.loaded), props);\n            } else {\n                return null;\n            }\n        }, [\n            props,\n            state\n        ]);\n    }\n    _s1(LoadableComponent, \"FetqI339RA+IfltT8VNzX8RMZ2Q=\", false, function() {\n        return [\n            useLoadableModule\n        ];\n    });\n    LoadableComponent.preload = ()=>init();\n    LoadableComponent.displayName = \"LoadableComponent\";\n    return /*#__PURE__*/ _react.default.forwardRef(LoadableComponent);\n}\nclass LoadableSubscription {\n    promise() {\n        return this._res.promise;\n    }\n    retry() {\n        this._clearTimeouts();\n        this._res = this._loadFn(this._opts.loader);\n        this._state = {\n            pastDelay: false,\n            timedOut: false\n        };\n        const { _res: res, _opts: opts } = this;\n        if (res.loading) {\n            if (typeof opts.delay === \"number\") {\n                if (opts.delay === 0) {\n                    this._state.pastDelay = true;\n                } else {\n                    this._delay = setTimeout(()=>{\n                        this._update({\n                            pastDelay: true\n                        });\n                    }, opts.delay);\n                }\n            }\n            if (typeof opts.timeout === \"number\") {\n                this._timeout = setTimeout(()=>{\n                    this._update({\n                        timedOut: true\n                    });\n                }, opts.timeout);\n            }\n        }\n        this._res.promise.then(()=>{\n            this._update({});\n            this._clearTimeouts();\n        }).catch((_err)=>{\n            this._update({});\n            this._clearTimeouts();\n        });\n        this._update({});\n    }\n    _update(partial) {\n        this._state = {\n            ...this._state,\n            error: this._res.error,\n            loaded: this._res.loaded,\n            loading: this._res.loading,\n            ...partial\n        };\n        this._callbacks.forEach((callback)=>callback());\n    }\n    _clearTimeouts() {\n        clearTimeout(this._delay);\n        clearTimeout(this._timeout);\n    }\n    getCurrentValue() {\n        return this._state;\n    }\n    subscribe(callback) {\n        this._callbacks.add(callback);\n        return ()=>{\n            this._callbacks.delete(callback);\n        };\n    }\n    constructor(loadFn, opts){\n        this._loadFn = loadFn;\n        this._opts = opts;\n        this._callbacks = new Set();\n        this._delay = null;\n        this._timeout = null;\n        this.retry();\n    }\n}\nfunction Loadable(opts) {\n    return createLoadableComponent(load, opts);\n}\n_c = Loadable;\nfunction flushInitializers(initializers, ids) {\n    let promises = [];\n    while(initializers.length){\n        let init = initializers.pop();\n        promises.push(init(ids));\n    }\n    return Promise.all(promises).then(()=>{\n        if (initializers.length) {\n            return flushInitializers(initializers, ids);\n        }\n    });\n}\nLoadable.preloadAll = ()=>{\n    return new Promise((resolveInitializers, reject)=>{\n        flushInitializers(ALL_INITIALIZERS).then(resolveInitializers, reject);\n    });\n};\nLoadable.preloadReady = (ids)=>{\n    if (ids === void 0) ids = [];\n    return new Promise((resolvePreload)=>{\n        const res = ()=>{\n            initialized = true;\n            return resolvePreload();\n        };\n        // We always will resolve, errors should be handled within loading UIs.\n        flushInitializers(READY_INITIALIZERS, ids).then(res, res);\n    });\n};\nif (true) {\n    window.__NEXT_PRELOADREADY = Loadable.preloadReady;\n}\nconst _default = Loadable; //# sourceMappingURL=loadable.shared-runtime.js.map\nvar _c;\n$RefreshReg$(_c, \"Loadable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/shared/lib/loadable.shared-runtime.js\n"));

/***/ }),

/***/ "./src/components/FederatedCheckout.tsx":
/*!**********************************************!*\
  !*** ./src/components/FederatedCheckout.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"./node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n// Dynamically import the checkout component from the remote\nconst CheckoutApp = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(()=>__webpack_require__.e(/*! import() */ \"webpack_container_remote_checkout_CheckoutApp\").then(__webpack_require__.t.bind(__webpack_require__, /*! checkout/CheckoutApp */ \"webpack/container/remote/checkout/CheckoutApp\", 23)), {\n    loadableGenerated: {\n        modules: [\n            \"components/FederatedCheckout.tsx -> \" + \"checkout/CheckoutApp\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"microfrontend-loading\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    textAlign: \"center\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDED2 Loading Checkout...\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Connecting to checkout microfrontend service\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"1rem\",\n                            fontSize: \"0.9rem\",\n                            color: \"#666\",\n                            fontFamily: \"monospace\"\n                        },\n                        children: \"checkout@http://localhost:3001/remoteEntry.js\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n            lineNumber: 8,\n            columnNumber: 5\n        }, undefined)\n});\n_c = CheckoutApp;\nconst FederatedCheckout = (param)=>{\n    let { onOrderComplete, onError, theme = \"default\", currency = \"USD\", locale = \"en-US\" } = param;\n    const handleOrderComplete = (orderData)=>{\n        console.log(\"✅ Order completed:\", orderData);\n        if (onOrderComplete) {\n            onOrderComplete(orderData);\n        }\n    };\n    const handleError = (error)=>{\n        console.error(\"❌ Checkout error:\", error);\n        if (onError) {\n            onError(error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"federated-checkout-container\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"microfrontend-loading\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\"\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"\\uD83D\\uDED2 Initializing Checkout...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 15\n                        }, void 0),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Loading federated module...\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 15\n                        }, void 0)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 13\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                lineNumber: 58,\n                columnNumber: 11\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckoutApp, {\n                theme: theme,\n                currency: currency,\n                locale: locale,\n                onOrderComplete: handleOrderComplete,\n                onError: handleError\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n                lineNumber: 66,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/FederatedCheckout.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = FederatedCheckout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (FederatedCheckout);\nvar _c, _c1;\n$RefreshReg$(_c, \"CheckoutApp\");\n$RefreshReg$(_c1, \"FederatedCheckout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9GZWRlcmF0ZWRDaGVja291dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0M7QUFDTDtBQUVuQyw0REFBNEQ7QUFDNUQsTUFBTUcsY0FBY0QsbURBQU9BLENBQUMsSUFBTSw2TkFBTzs7Ozs7O0lBQ3ZDRSxLQUFLO0lBQ0xDLFNBQVMsa0JBQ1AsOERBQUNDO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJRSxPQUFPO29CQUFFQyxXQUFXO2dCQUFTOztrQ0FDaEMsOERBQUNDO2tDQUFHOzs7Ozs7a0NBQ0osOERBQUNDO2tDQUFFOzs7Ozs7a0NBQ0gsOERBQUNMO3dCQUFJRSxPQUFPOzRCQUNWSSxXQUFXOzRCQUNYQyxVQUFVOzRCQUNWQyxPQUFPOzRCQUNQQyxZQUFZO3dCQUNkO2tDQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7S0FaTFo7QUE0Qk4sTUFBTWEsb0JBQXNEO1FBQUMsRUFDM0RDLGVBQWUsRUFDZkMsT0FBTyxFQUNQQyxRQUFRLFNBQVMsRUFDakJDLFdBQVcsS0FBSyxFQUNoQkMsU0FBUyxPQUFPLEVBQ2pCO0lBQ0MsTUFBTUMsc0JBQXNCLENBQUNDO1FBQzNCQyxRQUFRQyxHQUFHLENBQUMsc0JBQXNCRjtRQUNsQyxJQUFJTixpQkFBaUI7WUFDbkJBLGdCQUFnQk07UUFDbEI7SUFDRjtJQUVBLE1BQU1HLGNBQWMsQ0FBQ0M7UUFDbkJILFFBQVFHLEtBQUssQ0FBQyxxQkFBcUJBO1FBQ25DLElBQUlULFNBQVM7WUFDWEEsUUFBUVM7UUFDVjtJQUNGO0lBRUEscUJBQ0UsOERBQUNyQjtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDTiwyQ0FBUUE7WUFDUDJCLHdCQUNFLDhEQUFDdEI7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJRSxPQUFPO3dCQUFFQyxXQUFXO29CQUFTOztzQ0FDaEMsOERBQUNDO3NDQUFHOzs7Ozs7c0NBQ0osOERBQUNDO3NDQUFFOzs7Ozs7Ozs7Ozs7Ozs7OztzQkFLVCw0RUFBQ1I7Z0JBQ0NnQixPQUFPQTtnQkFDUEMsVUFBVUE7Z0JBQ1ZDLFFBQVFBO2dCQUNSSixpQkFBaUJLO2dCQUNqQkosU0FBU1E7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLbkI7TUEzQ01WO0FBNkNOLCtEQUFlQSxpQkFBaUJBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvRmVkZXJhdGVkQ2hlY2tvdXQudHN4PzA5NTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IFN1c3BlbnNlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcblxuLy8gRHluYW1pY2FsbHkgaW1wb3J0IHRoZSBjaGVja291dCBjb21wb25lbnQgZnJvbSB0aGUgcmVtb3RlXG5jb25zdCBDaGVja291dEFwcCA9IGR5bmFtaWMoKCkgPT4gaW1wb3J0KCdjaGVja291dC9DaGVja291dEFwcCcpLCB7XG4gIHNzcjogZmFsc2UsIC8vIERpc2FibGUgc2VydmVyLXNpZGUgcmVuZGVyaW5nIGZvciBmZWRlcmF0ZWQgbW9kdWxlc1xuICBsb2FkaW5nOiAoKSA9PiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaWNyb2Zyb250ZW5kLWxvYWRpbmdcIj5cbiAgICAgIDxkaXYgc3R5bGU9e3sgdGV4dEFsaWduOiAnY2VudGVyJyB9fT5cbiAgICAgICAgPGgzPvCfm5IgTG9hZGluZyBDaGVja291dC4uLjwvaDM+XG4gICAgICAgIDxwPkNvbm5lY3RpbmcgdG8gY2hlY2tvdXQgbWljcm9mcm9udGVuZCBzZXJ2aWNlPC9wPlxuICAgICAgICA8ZGl2IHN0eWxlPXt7IFxuICAgICAgICAgIG1hcmdpblRvcDogJzFyZW0nLCBcbiAgICAgICAgICBmb250U2l6ZTogJzAuOXJlbScsIFxuICAgICAgICAgIGNvbG9yOiAnIzY2NicsXG4gICAgICAgICAgZm9udEZhbWlseTogJ21vbm9zcGFjZSdcbiAgICAgICAgfX0+XG4gICAgICAgICAgY2hlY2tvdXRAaHR0cDovL2xvY2FsaG9zdDozMDAxL3JlbW90ZUVudHJ5LmpzXG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICksXG59KTtcblxuaW50ZXJmYWNlIEZlZGVyYXRlZENoZWNrb3V0UHJvcHMge1xuICBvbk9yZGVyQ29tcGxldGU/OiAob3JkZXJEYXRhOiBhbnkpID0+IHZvaWQ7XG4gIG9uRXJyb3I/OiAoZXJyb3I6IHN0cmluZykgPT4gdm9pZDtcbiAgdGhlbWU/OiBzdHJpbmc7XG4gIGN1cnJlbmN5Pzogc3RyaW5nO1xuICBsb2NhbGU/OiBzdHJpbmc7XG59XG5cbmNvbnN0IEZlZGVyYXRlZENoZWNrb3V0OiBSZWFjdC5GQzxGZWRlcmF0ZWRDaGVja291dFByb3BzPiA9ICh7XG4gIG9uT3JkZXJDb21wbGV0ZSxcbiAgb25FcnJvcixcbiAgdGhlbWUgPSAnZGVmYXVsdCcsXG4gIGN1cnJlbmN5ID0gJ1VTRCcsXG4gIGxvY2FsZSA9ICdlbi1VUycsXG59KSA9PiB7XG4gIGNvbnN0IGhhbmRsZU9yZGVyQ29tcGxldGUgPSAob3JkZXJEYXRhOiBhbnkpID0+IHtcbiAgICBjb25zb2xlLmxvZygn4pyFIE9yZGVyIGNvbXBsZXRlZDonLCBvcmRlckRhdGEpO1xuICAgIGlmIChvbk9yZGVyQ29tcGxldGUpIHtcbiAgICAgIG9uT3JkZXJDb21wbGV0ZShvcmRlckRhdGEpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVFcnJvciA9IChlcnJvcjogc3RyaW5nKSA9PiB7XG4gICAgY29uc29sZS5lcnJvcign4p2MIENoZWNrb3V0IGVycm9yOicsIGVycm9yKTtcbiAgICBpZiAob25FcnJvcikge1xuICAgICAgb25FcnJvcihlcnJvcik7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmZWRlcmF0ZWQtY2hlY2tvdXQtY29udGFpbmVyXCI+XG4gICAgICA8U3VzcGVuc2VcbiAgICAgICAgZmFsbGJhY2s9e1xuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWljcm9mcm9udGVuZC1sb2FkaW5nXCI+XG4gICAgICAgICAgICA8ZGl2IHN0eWxlPXt7IHRleHRBbGlnbjogJ2NlbnRlcicgfX0+XG4gICAgICAgICAgICAgIDxoMz7wn5uSIEluaXRpYWxpemluZyBDaGVja291dC4uLjwvaDM+XG4gICAgICAgICAgICAgIDxwPkxvYWRpbmcgZmVkZXJhdGVkIG1vZHVsZS4uLjwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICB9XG4gICAgICA+XG4gICAgICAgIDxDaGVja291dEFwcFxuICAgICAgICAgIHRoZW1lPXt0aGVtZX1cbiAgICAgICAgICBjdXJyZW5jeT17Y3VycmVuY3l9XG4gICAgICAgICAgbG9jYWxlPXtsb2NhbGV9XG4gICAgICAgICAgb25PcmRlckNvbXBsZXRlPXtoYW5kbGVPcmRlckNvbXBsZXRlfVxuICAgICAgICAgIG9uRXJyb3I9e2hhbmRsZUVycm9yfVxuICAgICAgICAvPlxuICAgICAgPC9TdXNwZW5zZT5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEZlZGVyYXRlZENoZWNrb3V0O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU3VzcGVuc2UiLCJkeW5hbWljIiwiQ2hlY2tvdXRBcHAiLCJzc3IiLCJsb2FkaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJ0ZXh0QWxpZ24iLCJoMyIsInAiLCJtYXJnaW5Ub3AiLCJmb250U2l6ZSIsImNvbG9yIiwiZm9udEZhbWlseSIsIkZlZGVyYXRlZENoZWNrb3V0Iiwib25PcmRlckNvbXBsZXRlIiwib25FcnJvciIsInRoZW1lIiwiY3VycmVuY3kiLCJsb2NhbGUiLCJoYW5kbGVPcmRlckNvbXBsZXRlIiwib3JkZXJEYXRhIiwiY29uc29sZSIsImxvZyIsImhhbmRsZUVycm9yIiwiZXJyb3IiLCJmYWxsYmFjayJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/FederatedCheckout.tsx\n"));

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst Layout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const isActive = (path)=>{\n        return router.pathname === path;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: \"\\uD83C\\uDFEA Shell App\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"nav\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: isActive(\"/\") ? \"active\" : \"\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/checkout\",\n                                        className: isActive(\"/checkout\") ? \"active\" : \"\",\n                                        children: \"Checkout\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: isActive(\"/about\") ? \"active\" : \"\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-content\",\n                style: {\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"footer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Shell App - Microfrontend Demo | Built with Next.js and Module Federation\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n"));

/***/ }),

/***/ "./src/pages/checkout.tsx":
/*!********************************!*\
  !*** ./src/pages/checkout.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"webpack/sharing/consume/default/react/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _components_FederatedCheckout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/FederatedCheckout */ \"./src/components/FederatedCheckout.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst CheckoutPage = ()=>{\n    _s();\n    const [orderStatus, setOrderStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"idle\");\n    const [orderData, setOrderData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleOrderComplete = (data)=>{\n        console.log(\"✅ Order completed successfully:\", data);\n        setOrderStatus(\"completed\");\n        setOrderData(data);\n    };\n    const handleCheckoutError = (error)=>{\n        console.error(\"❌ Checkout error:\", error);\n        setOrderStatus(\"error\");\n        setErrorMessage(error);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"page-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"page-title\",\n                        children: \"Checkout\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"page-subtitle\",\n                        children: \"Complete your purchase using our Module Federation checkout experience\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            orderStatus === \"idle\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading\",\n                                style: {\n                                    justifyContent: \"center\"\n                                },\n                                children: \"Ready to checkout...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, undefined),\n                            orderStatus === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading\",\n                                style: {\n                                    justifyContent: \"center\"\n                                },\n                                children: \"Processing your order...\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, undefined),\n                            orderStatus === \"completed\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"success\",\n                                children: \"✅ Order completed successfully!\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 13\n                            }, undefined),\n                            orderStatus === \"error\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"error\",\n                                children: [\n                                    \"❌ Checkout error: \",\n                                    errorMessage\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                style: {\n                    marginBottom: \"2rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDD27 Module Federation Integration\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This checkout experience is powered by a separate React application loaded through webpack Module Federation. The component is imported directly at build time and rendered as a native React component.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"1rem\",\n                            marginTop: \"1rem\",\n                            fontSize: \"0.9rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83C\\uDFD7️ Architecture:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 47\n                                    }, undefined),\n                                    \"Module Federation (Direct Import)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83C\\uDF10 Remote URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 44\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"localhost:3001/remoteEntry.js\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"⚛️ Technology:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 44\n                                    }, undefined),\n                                    \"React 18 + Webpack 5\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCE6 Module:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 40\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"checkout/CheckoutApp\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            orderStatus === \"completed\" && orderData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                style: {\n                    marginBottom: \"2rem\",\n                    background: \"#d4edda\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83C\\uDF89 Order Completed!\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"Thank you for your purchase. Your order has been processed successfully.\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#fff\",\n                            padding: \"1rem\",\n                            borderRadius: \"4px\",\n                            marginTop: \"1rem\",\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Order Data:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 41\n                            }, undefined),\n                            JSON.stringify(orderData, null, 2)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FederatedCheckout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onOrderComplete: handleOrderComplete,\n                    onError: handleCheckoutError,\n                    theme: \"default\",\n                    currency: \"USD\",\n                    locale: \"en-US\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, undefined),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                style: {\n                    marginTop: \"2rem\",\n                    background: \"#f8f9fa\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDC1B Debug Information\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Order Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    orderStatus\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Remote Module:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" checkout/CheckoutApp\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Remote URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" http://localhost:3001/remoteEntry.js\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Integration Type:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" Module Federation (Direct Import)\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, undefined),\n                            errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 35\n                                    }, undefined),\n                                    \" \",\n                                    errorMessage\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CheckoutPage, \"qM8YaHxECNmwhFGZmNyrAYR3d8A=\");\n_c = CheckoutPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CheckoutPage);\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/checkout.tsx\n"));

/***/ }),

/***/ "./node_modules/next/dynamic.js":
/*!**************************************!*\
  !*** ./node_modules/next/dynamic.js ***!
  \**************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/shared/lib/dynamic */ \"./node_modules/next/dist/shared/lib/dynamic.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzIiwibWFwcGluZ3MiOiJBQUFBLHVIQUFxRCIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9keW5hbWljLmpzPzczZDQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3Qvc2hhcmVkL2xpYi9keW5hbWljJylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/dynamic.js\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ }),

/***/ "./node_modules/next/router.js":
/*!*************************************!*\
  !*** ./node_modules/next/router.js ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/router */ \"./node_modules/next/dist/client/router.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9yb3V0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkdBQWdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L3JvdXRlci5qcz8xYmI2Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9yb3V0ZXInKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/router.js\n"));

/***/ }),

/***/ "webpack/container/reference/checkout":
/*!****************************************************************!*\
  !*** external "checkout@http://localhost:3001/remoteEntry.js" ***!
  \****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
var __webpack_error__ = new Error();
module.exports = new Promise(function(resolve, reject) {
	if(typeof checkout !== "undefined") return resolve();
	__webpack_require__.l("http://localhost:3001/remoteEntry.js", function(event) {
		if(typeof checkout !== "undefined") return resolve();
		var errorType = event && (event.type === 'load' ? 'missing' : event.type);
		var realSrc = event && event.target && event.target.src;
		__webpack_error__.message = 'Loading script failed.\n(' + errorType + ': ' + realSrc + ')';
		__webpack_error__.name = 'ScriptExternalLoadError';
		__webpack_error__.type = errorType;
		__webpack_error__.request = realSrc;
		reject(__webpack_error__);
	}, "checkout");
}).then(function() { return checkout; });

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);