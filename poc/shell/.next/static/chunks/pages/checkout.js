/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["pages/checkout"],{

/***/ "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!":
/*!**************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout! ***!
  \**************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/checkout\",\n      function () {\n        return __webpack_require__(/*! ./src/pages/checkout.tsx */ \"./src/pages/checkout.tsx\");\n      }\n    ]);\n    if(true) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/checkout\"])\n      });\n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWNsaWVudC1wYWdlcy1sb2FkZXIuanM/YWJzb2x1dGVQYWdlUGF0aD0lMkZob21lJTJGanVsaWFuJTJGcHJvamVjdHMlMkZwcml2YXRlJTJGYXJjaC1wcm9wb3NhbC1pYm0lMkZwb2MlMkZzaGVsbCUyRnNyYyUyRnBhZ2VzJTJGY2hlY2tvdXQudHN4JnBhZ2U9JTJGY2hlY2tvdXQhIiwibWFwcGluZ3MiOiI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLG1CQUFPLENBQUMsMERBQTBCO0FBQ2pEO0FBQ0E7QUFDQSxPQUFPLElBQVU7QUFDakIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0EsT0FBTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLz9jOTgwIl0sInNvdXJjZXNDb250ZW50IjpbIlxuICAgICh3aW5kb3cuX19ORVhUX1AgPSB3aW5kb3cuX19ORVhUX1AgfHwgW10pLnB1c2goW1xuICAgICAgXCIvY2hlY2tvdXRcIixcbiAgICAgIGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIHJlcXVpcmUoXCIuL3NyYy9wYWdlcy9jaGVja291dC50c3hcIik7XG4gICAgICB9XG4gICAgXSk7XG4gICAgaWYobW9kdWxlLmhvdCkge1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGZ1bmN0aW9uICgpIHtcbiAgICAgICAgd2luZG93Ll9fTkVYVF9QLnB1c2goW1wiL2NoZWNrb3V0XCJdKVxuICAgICAgfSk7XG4gICAgfVxuICAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js ***!
  \*****************************************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\n0 && (0);\nfunction _export(target, all) {\n    for(var name in all)Object.defineProperty(target, name, {\n        enumerable: true,\n        get: all[name]\n    });\n}\n_export(exports, {\n    ACTION_FAST_REFRESH: function() {\n        return ACTION_FAST_REFRESH;\n    },\n    ACTION_NAVIGATE: function() {\n        return ACTION_NAVIGATE;\n    },\n    ACTION_PREFETCH: function() {\n        return ACTION_PREFETCH;\n    },\n    ACTION_REFRESH: function() {\n        return ACTION_REFRESH;\n    },\n    ACTION_RESTORE: function() {\n        return ACTION_RESTORE;\n    },\n    ACTION_SERVER_ACTION: function() {\n        return ACTION_SERVER_ACTION;\n    },\n    ACTION_SERVER_PATCH: function() {\n        return ACTION_SERVER_PATCH;\n    },\n    PrefetchCacheEntryStatus: function() {\n        return PrefetchCacheEntryStatus;\n    },\n    PrefetchKind: function() {\n        return PrefetchKind;\n    },\n    isThenable: function() {\n        return isThenable;\n    }\n});\nconst ACTION_REFRESH = \"refresh\";\nconst ACTION_NAVIGATE = \"navigate\";\nconst ACTION_RESTORE = \"restore\";\nconst ACTION_SERVER_PATCH = \"server-patch\";\nconst ACTION_PREFETCH = \"prefetch\";\nconst ACTION_FAST_REFRESH = \"fast-refresh\";\nconst ACTION_SERVER_ACTION = \"server-action\";\nvar PrefetchKind;\n(function(PrefetchKind) {\n    PrefetchKind[\"AUTO\"] = \"auto\";\n    PrefetchKind[\"FULL\"] = \"full\";\n    PrefetchKind[\"TEMPORARY\"] = \"temporary\";\n})(PrefetchKind || (PrefetchKind = {}));\nvar PrefetchCacheEntryStatus;\n(function(PrefetchCacheEntryStatus) {\n    PrefetchCacheEntryStatus[\"fresh\"] = \"fresh\";\n    PrefetchCacheEntryStatus[\"reusable\"] = \"reusable\";\n    PrefetchCacheEntryStatus[\"expired\"] = \"expired\";\n    PrefetchCacheEntryStatus[\"stale\"] = \"stale\";\n})(PrefetchCacheEntryStatus || (PrefetchCacheEntryStatus = {}));\nfunction isThenable(value) {\n    // TODO: We don't gain anything from this abstraction. It's unsound, and only\n    // makes sense in the specific places where we use it. So it's better to keep\n    // the type coercion inline, instead of leaking this to other places in\n    // the codebase.\n    return value && (typeof value === \"object\" || typeof value === \"function\") && typeof value.then === \"function\";\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=router-reducer-types.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JvdXRlci1yZWR1Y2VyL3JvdXRlci1yZWR1Y2VyLXR5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztJQVlhQSxxQkFBbUI7ZUFBbkJBOztJQUpBQyxpQkFBZTtlQUFmQTs7SUFHQUMsaUJBQWU7ZUFBZkE7O0lBSkFDLGdCQUFjO2VBQWRBOztJQUVBQyxnQkFBYztlQUFkQTs7SUFJQUMsc0JBQW9CO2VBQXBCQTs7SUFIQUMscUJBQW1CO2VBQW5CQTs7Ozs7Ozs7SUF1UUdDLFlBQVU7ZUFBVkE7OztBQTFRVCxNQUFNSixpQkFBaUI7QUFDdkIsTUFBTUYsa0JBQWtCO0FBQ3hCLE1BQU1HLGlCQUFpQjtBQUN2QixNQUFNRSxzQkFBc0I7QUFDNUIsTUFBTUosa0JBQWtCO0FBQ3hCLE1BQU1GLHNCQUFzQjtBQUM1QixNQUFNSyx1QkFBdUI7O1VBdUl4QkcsWUFBQUE7Ozs7R0FBQUEsZ0JBQUFBLENBQUFBLGVBQUFBLENBQUFBLENBQUFBOztVQThEQUMsd0JBQUFBOzs7OztHQUFBQSw0QkFBQUEsQ0FBQUEsMkJBQUFBLENBQUFBLENBQUFBO0FBK0RMLFNBQVNGLFdBQVdHLEtBQVU7SUFDbkMsNkVBQTZFO0lBQzdFLDZFQUE2RTtJQUM3RSx1RUFBdUU7SUFDdkUsZ0JBQWdCO0lBQ2hCLE9BQ0VBLFNBQ0MsUUFBT0EsVUFBVSxZQUFZLE9BQU9BLFVBQVUsZUFDL0MsT0FBT0EsTUFBTUMsSUFBSSxLQUFLO0FBRTFCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi8uLi8uLi9zcmMvY2xpZW50L2NvbXBvbmVudHMvcm91dGVyLXJlZHVjZXIvcm91dGVyLXJlZHVjZXItdHlwZXMudHM/ZWYxYyJdLCJuYW1lcyI6WyJBQ1RJT05fRkFTVF9SRUZSRVNIIiwiQUNUSU9OX05BVklHQVRFIiwiQUNUSU9OX1BSRUZFVENIIiwiQUNUSU9OX1JFRlJFU0giLCJBQ1RJT05fUkVTVE9SRSIsIkFDVElPTl9TRVJWRVJfQUNUSU9OIiwiQUNUSU9OX1NFUlZFUl9QQVRDSCIsImlzVGhlbmFibGUiLCJQcmVmZXRjaEtpbmQiLCJQcmVmZXRjaENhY2hlRW50cnlTdGF0dXMiLCJ2YWx1ZSIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/get-domain-locale.js":
/*!************************************************************!*\
  !*** ./node_modules/next/dist/client/get-domain-locale.js ***!
  \************************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"getDomainLocale\", ({\n    enumerable: true,\n    get: function() {\n        return getDomainLocale;\n    }\n}));\nconst _normalizetrailingslash = __webpack_require__(/*! ./normalize-trailing-slash */ \"./node_modules/next/dist/client/normalize-trailing-slash.js\");\nconst basePath =  false || \"\";\nfunction getDomainLocale(path, locale, locales, domainLocales) {\n    if (false) {} else {\n        return false;\n    }\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=get-domain-locale.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9nZXQtZG9tYWluLWxvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7O21EQU9nQkE7OztlQUFBQTs7O29EQUoyQjtBQUUzQyxNQUFNQyxXQUFXQyxNQUFtQyxJQUFlO0FBRTVELFNBQVNGLGdCQUNkSyxJQUFZLEVBQ1pDLE1BQXVCLEVBQ3ZCQyxPQUFrQixFQUNsQkMsYUFBOEI7SUFFOUIsSUFBSU4sS0FBK0IsRUFBRSxFQWdCckMsTUFBTztRQUNMLE9BQU87SUFDVDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uLi8uLi9zcmMvY2xpZW50L2dldC1kb21haW4tbG9jYWxlLnRzPzFkNGUiXSwibmFtZXMiOlsiZ2V0RG9tYWluTG9jYWxlIiwiYmFzZVBhdGgiLCJwcm9jZXNzIiwiZW52IiwiX19ORVhUX1JPVVRFUl9CQVNFUEFUSCIsInBhdGgiLCJsb2NhbGUiLCJsb2NhbGVzIiwiZG9tYWluTG9jYWxlcyIsIl9fTkVYVF9JMThOX1NVUFBPUlQiLCJub3JtYWxpemVMb2NhbGVQYXRoIiwicmVxdWlyZSIsImRldGVjdERvbWFpbkxvY2FsZSIsInRhcmdldCIsImRldGVjdGVkTG9jYWxlIiwiZG9tYWluIiwidW5kZWZpbmVkIiwicHJvdG8iLCJodHRwIiwiZmluYWxMb2NhbGUiLCJkZWZhdWx0TG9jYWxlIiwibm9ybWFsaXplUGF0aFRyYWlsaW5nU2xhc2giXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/get-domain-locale.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/link.js":
/*!***********************************************!*\
  !*** ./node_modules/next/dist/client/link.js ***!
  \***********************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar _s = $RefreshSig$();\n\"use strict\";\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return _default;\n    }\n}));\nconst _interop_require_default = __webpack_require__(/*! @swc/helpers/_/_interop_require_default */ \"./node_modules/@swc/helpers/esm/_interop_require_default.js\");\nconst _jsxruntime = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\nconst _react = /*#__PURE__*/ _interop_require_default._(__webpack_require__(/*! react */ \"./node_modules/react/index.js\"));\nconst _resolvehref = __webpack_require__(/*! ./resolve-href */ \"./node_modules/next/dist/client/resolve-href.js\");\nconst _islocalurl = __webpack_require__(/*! ../shared/lib/router/utils/is-local-url */ \"./node_modules/next/dist/shared/lib/router/utils/is-local-url.js\");\nconst _formaturl = __webpack_require__(/*! ../shared/lib/router/utils/format-url */ \"./node_modules/next/dist/shared/lib/router/utils/format-url.js\");\nconst _utils = __webpack_require__(/*! ../shared/lib/utils */ \"./node_modules/next/dist/shared/lib/utils.js\");\nconst _addlocale = __webpack_require__(/*! ./add-locale */ \"./node_modules/next/dist/client/add-locale.js\");\nconst _routercontextsharedruntime = __webpack_require__(/*! ../shared/lib/router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/router-context.shared-runtime.js\");\nconst _approutercontextsharedruntime = __webpack_require__(/*! ../shared/lib/app-router-context.shared-runtime */ \"./node_modules/next/dist/shared/lib/app-router-context.shared-runtime.js\");\nconst _useintersection = __webpack_require__(/*! ./use-intersection */ \"./node_modules/next/dist/client/use-intersection.js\");\nconst _getdomainlocale = __webpack_require__(/*! ./get-domain-locale */ \"./node_modules/next/dist/client/get-domain-locale.js\");\nconst _addbasepath = __webpack_require__(/*! ./add-base-path */ \"./node_modules/next/dist/client/add-base-path.js\");\nconst _routerreducertypes = __webpack_require__(/*! ./components/router-reducer/router-reducer-types */ \"./node_modules/next/dist/client/components/router-reducer/router-reducer-types.js\");\nconst prefetched = new Set();\nfunction prefetch(router, href, as, options, appOptions, isAppRouter) {\n    if (false) {}\n    // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    if (!isAppRouter && !(0, _islocalurl.isLocalURL)(href)) {\n        return;\n    }\n    // We should only dedupe requests when experimental.optimisticClientCache is\n    // disabled.\n    if (!options.bypassPrefetchedCheck) {\n        const locale = typeof options.locale !== \"undefined\" ? options.locale : \"locale\" in router ? router.locale : undefined;\n        const prefetchedKey = href + \"%\" + as + \"%\" + locale;\n        // If we've already fetched the key, then don't prefetch it again!\n        if (prefetched.has(prefetchedKey)) {\n            return;\n        }\n        // Mark this URL as prefetched.\n        prefetched.add(prefetchedKey);\n    }\n    const doPrefetch = async ()=>{\n        if (isAppRouter) {\n            // note that `appRouter.prefetch()` is currently sync,\n            // so we have to wrap this call in an async function to be able to catch() errors below.\n            return router.prefetch(href, appOptions);\n        } else {\n            return router.prefetch(href, as, options);\n        }\n    };\n    // Prefetch the JSON page if asked (only in the client)\n    // We need to handle a prefetch error here since we may be\n    // loading with priority which can reject but we don't\n    // want to force navigation since this is only a prefetch\n    doPrefetch().catch((err)=>{\n        if (true) {\n            // rethrow to show invalid URL errors\n            throw err;\n        }\n    });\n}\nfunction isModifiedEvent(event) {\n    const eventTarget = event.currentTarget;\n    const target = eventTarget.getAttribute(\"target\");\n    return target && target !== \"_self\" || event.metaKey || event.ctrlKey || event.shiftKey || event.altKey || // triggers resource download\n    event.nativeEvent && event.nativeEvent.which === 2;\n}\nfunction linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter) {\n    const { nodeName } = e.currentTarget;\n    // anchors inside an svg have a lowercase nodeName\n    const isAnchorNodeName = nodeName.toUpperCase() === \"A\";\n    if (isAnchorNodeName && (isModifiedEvent(e) || // app-router supports external urls out of the box so it shouldn't short-circuit here as support for e.g. `replace` is added in the app-router.\n    !isAppRouter && !(0, _islocalurl.isLocalURL)(href))) {\n        // ignore click for browser’s default behavior\n        return;\n    }\n    e.preventDefault();\n    const navigate = ()=>{\n        // If the router is an NextRouter instance it will have `beforePopState`\n        const routerScroll = scroll != null ? scroll : true;\n        if (\"beforePopState\" in router) {\n            router[replace ? \"replace\" : \"push\"](href, as, {\n                shallow,\n                locale,\n                scroll: routerScroll\n            });\n        } else {\n            router[replace ? \"replace\" : \"push\"](as || href, {\n                scroll: routerScroll\n            });\n        }\n    };\n    if (isAppRouter) {\n        _react.default.startTransition(navigate);\n    } else {\n        navigate();\n    }\n}\nfunction formatStringOrUrl(urlObjOrString) {\n    if (typeof urlObjOrString === \"string\") {\n        return urlObjOrString;\n    }\n    return (0, _formaturl.formatUrl)(urlObjOrString);\n}\n/**\n * A React component that extends the HTML `<a>` element to provide [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation between routes.\n *\n * It is the primary way to navigate between routes in Next.js.\n *\n * Read more: [Next.js docs: `<Link>`](https://nextjs.org/docs/app/api-reference/components/link)\n */ const Link = /*#__PURE__*/ _s(_react.default.forwardRef(_c = _s(function LinkComponent(props, forwardedRef) {\n    _s();\n    let children;\n    const { href: hrefProp, as: asProp, children: childrenProp, prefetch: prefetchProp = null, passHref, replace, shallow, scroll, locale, onClick, onMouseEnter: onMouseEnterProp, onTouchStart: onTouchStartProp, legacyBehavior = false, ...restProps } = props;\n    children = childrenProp;\n    if (legacyBehavior && (typeof children === \"string\" || typeof children === \"number\")) {\n        children = /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n            children: children\n        });\n    }\n    const pagesRouter = _react.default.useContext(_routercontextsharedruntime.RouterContext);\n    const appRouter = _react.default.useContext(_approutercontextsharedruntime.AppRouterContext);\n    const router = pagesRouter != null ? pagesRouter : appRouter;\n    // We're in the app directory if there is no pages router.\n    const isAppRouter = !pagesRouter;\n    const prefetchEnabled = prefetchProp !== false;\n    /**\n     * The possible states for prefetch are:\n     * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n     * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n     * - false: we will not prefetch if in the viewport at all\n     */ const appPrefetchKind = prefetchProp === null ? _routerreducertypes.PrefetchKind.AUTO : _routerreducertypes.PrefetchKind.FULL;\n    if (true) {\n        function createPropError(args) {\n            return new Error(\"Failed prop type: The prop `\" + args.key + \"` expects a \" + args.expected + \" in `<Link>`, but got `\" + args.actual + \"` instead.\" + ( true ? \"\\nOpen your browser's console to view the Component stack trace.\" : 0));\n        }\n        // TypeScript trick for type-guarding:\n        const requiredPropsGuard = {\n            href: true\n        };\n        const requiredProps = Object.keys(requiredPropsGuard);\n        requiredProps.forEach((key)=>{\n            if (key === \"href\") {\n                if (props[key] == null || typeof props[key] !== \"string\" && typeof props[key] !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: props[key] === null ? \"null\" : typeof props[key]\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // TypeScript trick for type-guarding:\n        const optionalPropsGuard = {\n            as: true,\n            replace: true,\n            scroll: true,\n            shallow: true,\n            passHref: true,\n            prefetch: true,\n            locale: true,\n            onClick: true,\n            onMouseEnter: true,\n            onTouchStart: true,\n            legacyBehavior: true\n        };\n        const optionalProps = Object.keys(optionalPropsGuard);\n        optionalProps.forEach((key)=>{\n            const valType = typeof props[key];\n            if (key === \"as\") {\n                if (props[key] && valType !== \"string\" && valType !== \"object\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string` or `object`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"locale\") {\n                if (props[key] && valType !== \"string\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`string`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"onClick\" || key === \"onMouseEnter\" || key === \"onTouchStart\") {\n                if (props[key] && valType !== \"function\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`function`\",\n                        actual: valType\n                    });\n                }\n            } else if (key === \"replace\" || key === \"scroll\" || key === \"shallow\" || key === \"passHref\" || key === \"prefetch\" || key === \"legacyBehavior\") {\n                if (props[key] != null && valType !== \"boolean\") {\n                    throw createPropError({\n                        key,\n                        expected: \"`boolean`\",\n                        actual: valType\n                    });\n                }\n            } else {\n                // TypeScript trick for type-guarding:\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                const _ = key;\n            }\n        });\n        // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const hasWarned = _react.default.useRef(false);\n        if (props.prefetch && !hasWarned.current && !isAppRouter) {\n            hasWarned.current = true;\n            console.warn(\"Next.js auto-prefetches automatically based on viewport. The prefetch attribute is no longer needed. More: https://nextjs.org/docs/messages/prefetch-true-deprecated\");\n        }\n    }\n    if (true) {\n        if (isAppRouter && !asProp) {\n            let href;\n            if (typeof hrefProp === \"string\") {\n                href = hrefProp;\n            } else if (typeof hrefProp === \"object\" && typeof hrefProp.pathname === \"string\") {\n                href = hrefProp.pathname;\n            }\n            if (href) {\n                const hasDynamicSegment = href.split(\"/\").some((segment)=>segment.startsWith(\"[\") && segment.endsWith(\"]\"));\n                if (hasDynamicSegment) {\n                    throw new Error(\"Dynamic href `\" + href + \"` found in <Link> while using the `/app` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href\");\n                }\n            }\n        }\n    }\n    const { href, as } = _react.default.useMemo(()=>{\n        if (!pagesRouter) {\n            const resolvedHref = formatStringOrUrl(hrefProp);\n            return {\n                href: resolvedHref,\n                as: asProp ? formatStringOrUrl(asProp) : resolvedHref\n            };\n        }\n        const [resolvedHref, resolvedAs] = (0, _resolvehref.resolveHref)(pagesRouter, hrefProp, true);\n        return {\n            href: resolvedHref,\n            as: asProp ? (0, _resolvehref.resolveHref)(pagesRouter, asProp) : resolvedAs || resolvedHref\n        };\n    }, [\n        pagesRouter,\n        hrefProp,\n        asProp\n    ]);\n    const previousHref = _react.default.useRef(href);\n    const previousAs = _react.default.useRef(as);\n    // This will return the first child, if multiple are provided it will throw an error\n    let child;\n    if (legacyBehavior) {\n        if (true) {\n            if (onClick) {\n                console.warn('\"onClick\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link');\n            }\n            if (onMouseEnterProp) {\n                console.warn('\"onMouseEnter\" was passed to <Link> with `href` of `' + hrefProp + '` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link');\n            }\n            try {\n                child = _react.default.Children.only(children);\n            } catch (err) {\n                if (!children) {\n                    throw new Error(\"No children were passed to <Link> with `href` of `\" + hrefProp + \"` but one child is required https://nextjs.org/docs/messages/link-no-children\");\n                }\n                throw new Error(\"Multiple children were passed to <Link> with `href` of `\" + hrefProp + \"` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children\" + ( true ? \" \\nOpen your browser's console to view the Component stack trace.\" : 0));\n            }\n        } else {}\n    } else {\n        if (true) {\n            if ((children == null ? void 0 : children.type) === \"a\") {\n                throw new Error(\"Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor\");\n            }\n        }\n    }\n    const childRef = legacyBehavior ? child && typeof child === \"object\" && child.ref : forwardedRef;\n    const [setIntersectionRef, isVisible, resetVisible] = (0, _useintersection.useIntersection)({\n        rootMargin: \"200px\"\n    });\n    const setRef = _react.default.useCallback((el)=>{\n        // Before the link getting observed, check if visible state need to be reset\n        if (previousAs.current !== as || previousHref.current !== href) {\n            resetVisible();\n            previousAs.current = as;\n            previousHref.current = href;\n        }\n        setIntersectionRef(el);\n        if (childRef) {\n            if (typeof childRef === \"function\") childRef(el);\n            else if (typeof childRef === \"object\") {\n                childRef.current = el;\n            }\n        }\n    }, [\n        as,\n        childRef,\n        href,\n        resetVisible,\n        setIntersectionRef\n    ]);\n    // Prefetch the URL if we haven't already and it's visible.\n    _react.default.useEffect(()=>{\n        // in dev, we only prefetch on hover to avoid wasting resources as the prefetch will trigger compiling the page.\n        if (true) {\n            return;\n        }\n        if (!router) {\n            return;\n        }\n        // If we don't need to prefetch the URL, don't do prefetch.\n        if (!isVisible || !prefetchEnabled) {\n            return;\n        }\n        // Prefetch the URL.\n        prefetch(router, href, as, {\n            locale\n        }, {\n            kind: appPrefetchKind\n        }, isAppRouter);\n    }, [\n        as,\n        href,\n        isVisible,\n        locale,\n        prefetchEnabled,\n        pagesRouter == null ? void 0 : pagesRouter.locale,\n        router,\n        isAppRouter,\n        appPrefetchKind\n    ]);\n    const childProps = {\n        ref: setRef,\n        onClick (e) {\n            if (true) {\n                if (!e) {\n                    throw new Error('Component rendered inside next/link has to pass click event to \"onClick\" prop.');\n                }\n            }\n            if (!legacyBehavior && typeof onClick === \"function\") {\n                onClick(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onClick === \"function\") {\n                child.props.onClick(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (e.defaultPrevented) {\n                return;\n            }\n            linkClicked(e, router, href, as, replace, shallow, scroll, locale, isAppRouter);\n        },\n        onMouseEnter (e) {\n            if (!legacyBehavior && typeof onMouseEnterProp === \"function\") {\n                onMouseEnterProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onMouseEnter === \"function\") {\n                child.props.onMouseEnter(e);\n            }\n            if (!router) {\n                return;\n            }\n            if ((!prefetchEnabled || \"development\" === \"development\") && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        },\n        onTouchStart:  false ? 0 : function onTouchStart(e) {\n            if (!legacyBehavior && typeof onTouchStartProp === \"function\") {\n                onTouchStartProp(e);\n            }\n            if (legacyBehavior && child.props && typeof child.props.onTouchStart === \"function\") {\n                child.props.onTouchStart(e);\n            }\n            if (!router) {\n                return;\n            }\n            if (!prefetchEnabled && isAppRouter) {\n                return;\n            }\n            prefetch(router, href, as, {\n                locale,\n                priority: true,\n                // @see {https://github.com/vercel/next.js/discussions/40268?sort=top#discussioncomment-3572642}\n                bypassPrefetchedCheck: true\n            }, {\n                kind: appPrefetchKind\n            }, isAppRouter);\n        }\n    };\n    // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n    // defined, we specify the current 'href', so that repetition is not needed by the user.\n    // If the url is absolute, we can bypass the logic to prepend the domain and locale.\n    if ((0, _utils.isAbsoluteUrl)(as)) {\n        childProps.href = as;\n    } else if (!legacyBehavior || passHref || child.type === \"a\" && !(\"href\" in child.props)) {\n        const curLocale = typeof locale !== \"undefined\" ? locale : pagesRouter == null ? void 0 : pagesRouter.locale;\n        // we only render domain locales if we are currently on a domain locale\n        // so that locale links are still visitable in development/preview envs\n        const localeDomain = (pagesRouter == null ? void 0 : pagesRouter.isLocaleDomain) && (0, _getdomainlocale.getDomainLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.locales, pagesRouter == null ? void 0 : pagesRouter.domainLocales);\n        childProps.href = localeDomain || (0, _addbasepath.addBasePath)((0, _addlocale.addLocale)(as, curLocale, pagesRouter == null ? void 0 : pagesRouter.defaultLocale));\n    }\n    return legacyBehavior ? /*#__PURE__*/ _react.default.cloneElement(child, childProps) : /*#__PURE__*/ (0, _jsxruntime.jsx)(\"a\", {\n        ...restProps,\n        ...childProps,\n        children: children\n    });\n}, \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\")), \"wKD5mb5mk47bkaStGb/Fvd6RWZE=\");\n_c1 = Link;\nconst _default = Link;\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=link.js.map\nvar _c, _c1;\n$RefreshReg$(_c, \"Link$_react.default.forwardRef\");\n$RefreshReg$(_c1, \"Link\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/link.js\n"));

/***/ }),

/***/ "./node_modules/next/dist/client/use-intersection.js":
/*!***********************************************************!*\
  !*** ./node_modules/next/dist/client/use-intersection.js ***!
  \***********************************************************/
/***/ (function(module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"useIntersection\", ({\n    enumerable: true,\n    get: function() {\n        return useIntersection;\n    }\n}));\nconst _react = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\nconst _requestidlecallback = __webpack_require__(/*! ./request-idle-callback */ \"./node_modules/next/dist/client/request-idle-callback.js\");\nconst hasIntersectionObserver = typeof IntersectionObserver === \"function\";\nconst observers = new Map();\nconst idList = [];\nfunction createObserver(options) {\n    const id = {\n        root: options.root || null,\n        margin: options.rootMargin || \"\"\n    };\n    const existing = idList.find((obj)=>obj.root === id.root && obj.margin === id.margin);\n    let instance;\n    if (existing) {\n        instance = observers.get(existing);\n        if (instance) {\n            return instance;\n        }\n    }\n    const elements = new Map();\n    const observer = new IntersectionObserver((entries)=>{\n        entries.forEach((entry)=>{\n            const callback = elements.get(entry.target);\n            const isVisible = entry.isIntersecting || entry.intersectionRatio > 0;\n            if (callback && isVisible) {\n                callback(isVisible);\n            }\n        });\n    }, options);\n    instance = {\n        id,\n        observer,\n        elements\n    };\n    idList.push(id);\n    observers.set(id, instance);\n    return instance;\n}\nfunction observe(element, callback, options) {\n    const { id, observer, elements } = createObserver(options);\n    elements.set(element, callback);\n    observer.observe(element);\n    return function unobserve() {\n        elements.delete(element);\n        observer.unobserve(element);\n        // Destroy observer when there's nothing left to watch:\n        if (elements.size === 0) {\n            observer.disconnect();\n            observers.delete(id);\n            const index = idList.findIndex((obj)=>obj.root === id.root && obj.margin === id.margin);\n            if (index > -1) {\n                idList.splice(index, 1);\n            }\n        }\n    };\n}\nfunction useIntersection(param) {\n    let { rootRef, rootMargin, disabled } = param;\n    const isDisabled = disabled || !hasIntersectionObserver;\n    const [visible, setVisible] = (0, _react.useState)(false);\n    const elementRef = (0, _react.useRef)(null);\n    const setElement = (0, _react.useCallback)((element)=>{\n        elementRef.current = element;\n    }, []);\n    (0, _react.useEffect)(()=>{\n        if (hasIntersectionObserver) {\n            if (isDisabled || visible) return;\n            const element = elementRef.current;\n            if (element && element.tagName) {\n                const unobserve = observe(element, (isVisible)=>isVisible && setVisible(isVisible), {\n                    root: rootRef == null ? void 0 : rootRef.current,\n                    rootMargin\n                });\n                return unobserve;\n            }\n        } else {\n            if (!visible) {\n                const idleCallback = (0, _requestidlecallback.requestIdleCallback)(()=>setVisible(true));\n                return ()=>(0, _requestidlecallback.cancelIdleCallback)(idleCallback);\n            }\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        isDisabled,\n        rootMargin,\n        rootRef,\n        visible,\n        elementRef.current\n    ]);\n    const resetVisible = (0, _react.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return [\n        setElement,\n        visible,\n        resetVisible\n    ];\n}\nif ((typeof exports.default === \"function\" || typeof exports.default === \"object\" && exports.default !== null) && typeof exports.default.__esModule === \"undefined\") {\n    Object.defineProperty(exports.default, \"__esModule\", {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=use-intersection.js.map\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/client/use-intersection.js\n"));

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\nvar _s = $RefreshSig$();\n\n\n\nconst Layout = (param)=>{\n    let { children } = param;\n    _s();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const isActive = (path)=>{\n        return router.pathname === path;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: \"\\uD83C\\uDFEA Shell App\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"nav\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: isActive(\"/\") ? \"active\" : \"\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/checkout\",\n                                        className: isActive(\"/checkout\") ? \"active\" : \"\",\n                                        children: \"Checkout\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: isActive(\"/about\") ? \"active\" : \"\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-content\",\n                style: {\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"footer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Shell App - Microfrontend Demo | Built with Next.js and Module Federation\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Layout, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Layout;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Layout);\nvar _c;\n$RefreshReg$(_c, \"Layout\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n"));

/***/ }),

/***/ "./src/components/MicrofrontendWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/MicrofrontendWrapper.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMicrofrontend */ \"./src/hooks/useMicrofrontend.ts\");\n\nvar _s = $RefreshSig$();\n\n\nconst MicrofrontendWrapper = (param)=>{\n    let { name, url, elementName, fallback, errorFallback, onLoad, onError, className = \"\", style = {}, ...webComponentProps } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { isLoading, isLoaded, error, loadMicrofrontend } = (0,_hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_2__.useMicrofrontend)({\n        name,\n        url,\n        elementName\n    });\n    // Load the microfrontend when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMicrofrontend();\n    }, []);\n    // Call callbacks when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && onLoad) {\n            onLoad();\n        }\n    }, [\n        isLoaded,\n        onLoad\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    // Render the web component when loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && containerRef.current) {\n            // Check if the web component is already in the container\n            const existingElement = containerRef.current.querySelector(elementName);\n            if (!existingElement) {\n                // Create the web component element\n                const webComponent = document.createElement(elementName);\n                // Set attributes from props\n                Object.entries(webComponentProps).forEach((param)=>{\n                    let [key, value] = param;\n                    if (value !== undefined && value !== null) {\n                        webComponent.setAttribute(key, String(value));\n                    }\n                });\n                // Add to container\n                containerRef.current.appendChild(webComponent);\n                console.log(\"✅ Web component \".concat(elementName, \" rendered successfully\"));\n            }\n        }\n    }, [\n        isLoaded,\n        elementName,\n        webComponentProps\n    ]);\n    // Default fallbacks\n    const defaultFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                \"Loading \",\n                name,\n                \" microfrontend...\"\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n    const defaultErrorFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-error\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                children: [\n                    \"⚠️ Failed to load \",\n                    name\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Make sure the \",\n                    name,\n                    \" microfrontend is running on\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: url.replace(\"/remoteEntry.js\", \"\")\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn btn-secondary\",\n                onClick: loadMicrofrontend,\n                style: {\n                    marginTop: \"1rem\"\n                },\n                children: \"Retry\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-container \".concat(className),\n        style: style,\n        children: error ? errorFallback || defaultErrorFallback : isLoading ? fallback || defaultFallback : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: containerRef,\n            style: {\n                width: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n            lineNumber: 114,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n_s(MicrofrontendWrapper, \"4aIwh/X6eCEouWdLs8g1uB4JyrM=\", false, function() {\n    return [\n        _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_2__.useMicrofrontend\n    ];\n});\n_c = MicrofrontendWrapper;\n/* harmony default export */ __webpack_exports__[\"default\"] = (MicrofrontendWrapper);\nvar _c;\n$RefreshReg$(_c, \"MicrofrontendWrapper\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MicrofrontendWrapper.tsx\n"));

/***/ }),

/***/ "./src/hooks/useMicrofrontend.ts":
/*!***************************************!*\
  !*** ./src/hooks/useMicrofrontend.ts ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MICROFRONTEND_CONFIGS: function() { return /* binding */ MICROFRONTEND_CONFIGS; },\n/* harmony export */   useMicrofrontend: function() { return /* binding */ useMicrofrontend; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMicrofrontend = (config)=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const loadMicrofrontend = async ()=>{\n        if (isLoaded || isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Check if the script is already loaded\n            const existingScript = document.querySelector('script[src=\"'.concat(config.url, '\"]'));\n            if (existingScript) {\n                setIsLoaded(true);\n                setIsLoading(false);\n                return;\n            }\n            // Create and load the script\n            const script = document.createElement(\"script\");\n            script.src = config.url;\n            script.type = \"text/javascript\";\n            script.async = true;\n            // Handle script load success\n            script.onload = ()=>{\n                console.log(\"✅ Microfrontend \".concat(config.name, \" loaded successfully\"));\n                // If it's a web component, wait for it to be defined\n                if (config.elementName) {\n                    customElements.whenDefined(config.elementName).then(()=>{\n                        console.log(\"✅ Web component \".concat(config.elementName, \" is ready\"));\n                        setIsLoaded(true);\n                        setIsLoading(false);\n                    }).catch((err)=>{\n                        console.error(\"❌ Error waiting for web component \".concat(config.elementName, \":\"), err);\n                        setError(\"Failed to initialize web component: \".concat(config.elementName));\n                        setIsLoading(false);\n                    });\n                } else {\n                    setIsLoaded(true);\n                    setIsLoading(false);\n                }\n            };\n            // Handle script load error\n            script.onerror = ()=>{\n                const errorMsg = \"Failed to load microfrontend from \".concat(config.url);\n                console.error(\"❌ \".concat(errorMsg));\n                setError(errorMsg);\n                setIsLoading(false);\n            };\n            // Add script to document\n            document.head.appendChild(script);\n        } catch (err) {\n            const errorMsg = \"Error loading microfrontend: \".concat(err);\n            console.error(\"❌ \".concat(errorMsg));\n            setError(errorMsg);\n            setIsLoading(false);\n        }\n    };\n    return {\n        isLoading,\n        isLoaded,\n        error,\n        loadMicrofrontend\n    };\n};\n// Predefined configurations for known microfrontends\nconst MICROFRONTEND_CONFIGS = {\n    checkout: {\n        name: \"checkout\",\n        url: \"http://localhost:3001/remoteEntry.js\",\n        elementName: \"checkout-widget\"\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useMicrofrontend.ts\n"));

/***/ }),

/***/ "./src/pages/checkout.tsx":
/*!********************************!*\
  !*** ./src/pages/checkout.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _components_MicrofrontendWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MicrofrontendWrapper */ \"./src/components/MicrofrontendWrapper.tsx\");\n/* harmony import */ var _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useMicrofrontend */ \"./src/hooks/useMicrofrontend.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst CheckoutPage = ()=>{\n    _s();\n    const [loadStatus, setLoadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleMicrofrontendLoad = ()=>{\n        console.log(\"✅ Checkout microfrontend loaded successfully\");\n        setLoadStatus(\"loaded\");\n    };\n    const handleMicrofrontendError = (error)=>{\n        console.error(\"❌ Checkout microfrontend failed to load:\", error);\n        setLoadStatus(\"error\");\n        setErrorMessage(error);\n    };\n    const customLoadingFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"\\uD83D\\uDED2 Loading Checkout...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Connecting to checkout microfrontend service\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: \"1rem\",\n                        fontSize: \"0.9rem\",\n                        color: \"#666\",\n                        fontFamily: \"monospace\"\n                    },\n                    children: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n    const customErrorFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-error\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                children: \"⚠️ Checkout Service Unavailable\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"We're having trouble loading the checkout experience.\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fff\",\n                    padding: \"1rem\",\n                    borderRadius: \"4px\",\n                    margin: \"1rem 0\",\n                    border: \"1px solid #f56565\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    \" \",\n                    errorMessage\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Troubleshooting:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            textAlign: \"left\",\n                            marginTop: \"0.5rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Make sure the checkout service is running on port 3001\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Run: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"cd poc/checkout && npm start\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 20\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Check that \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"http://localhost:3001\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 26\n                                    }, undefined),\n                                    \" is accessible\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn\",\n                onClick: ()=>window.location.reload(),\n                style: {\n                    background: \"#007bff\",\n                    color: \"white\"\n                },\n                children: \"\\uD83D\\uDD04 Retry Loading\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MicrofrontendWrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    name: \"checkout\",\n                    url: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url,\n                    elementName: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.elementName,\n                    onLoad: handleMicrofrontendLoad,\n                    onError: handleMicrofrontendError,\n                    fallback: customLoadingFallback,\n                    errorFallback: customErrorFallback\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                style: {\n                    marginTop: \"2rem\",\n                    background: \"#f8f9fa\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDC1B Debug Information\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Load Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    loadStatus\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Service URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Element Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.elementName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 35\n                                    }, undefined),\n                                    \" \",\n                                    errorMessage\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CheckoutPage, \"AA+mcba4iRXIt1KGmkwT5jo9vAs=\");\n_c = CheckoutPage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CheckoutPage);\nvar _c;\n$RefreshReg$(_c, \"CheckoutPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/checkout.tsx\n"));

/***/ }),

/***/ "./node_modules/next/link.js":
/*!***********************************!*\
  !*** ./node_modules/next/link.js ***!
  \***********************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/link */ \"./node_modules/next/dist/client/link.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzIiwibWFwcGluZ3MiOiJBQUFBLHlHQUE4QyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9saW5rLmpzPzc1YjMiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvY2xpZW50L2xpbmsnKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/link.js\n"));

/***/ }),

/***/ "./node_modules/next/router.js":
/*!*************************************!*\
  !*** ./node_modules/next/router.js ***!
  \*************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("module.exports = __webpack_require__(/*! ./dist/client/router */ \"./node_modules/next/dist/client/router.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9yb3V0ZXIuanMiLCJtYXBwaW5ncyI6IkFBQUEsNkdBQWdEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9uZXh0L3JvdXRlci5qcz8xYmI2Il0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9kaXN0L2NsaWVudC9yb3V0ZXInKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/router.js\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["pages/_app","main"], function() { return __webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=%2Fhome%2Fjulian%2Fprojects%2Fprivate%2Farch-proposal-ibm%2Fpoc%2Fshell%2Fsrc%2Fpages%2Fcheckout.tsx&page=%2Fcheckout!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);