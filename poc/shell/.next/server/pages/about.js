/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/about";
exports.ids = ["pages/about"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fabout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fabout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fabout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fabout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/about.tsx */ \"./src/pages/about.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/about\",\n        pathname: \"/about\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_about_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fabout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fabout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst Layout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const isActive = (path)=>{\n        return router.pathname === path;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: \"\\uD83C\\uDFEA Shell App\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"nav\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: isActive(\"/\") ? \"active\" : \"\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/checkout\",\n                                        className: isActive(\"/checkout\") ? \"active\" : \"\",\n                                        children: \"Checkout\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: isActive(\"/about\") ? \"active\" : \"\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-content\",\n                style: {\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"footer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Shell App - Microfrontend Demo | Built with Next.js and Module Federation\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Shell App - Microfrontend Demo\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Next.js shell application demonstrating microfrontend integration\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDNkI7QUFDQztBQUVmLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0U7OzBCQUNFLDhEQUFDSCxrREFBSUE7O2tDQUNILDhEQUFDSTtrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXhCLDhEQUFDUjtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NoZWxsLWFwcC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+U2hlbGwgQXBwIC0gTWljcm9mcm9udGVuZCBEZW1vPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIk5leHQuanMgc2hlbGwgYXBwbGljYXRpb24gZGVtb25zdHJhdGluZyBtaWNyb2Zyb250ZW5kIGludGVncmF0aW9uXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJ0aXRsZSIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOzs7OzswQkFHTCw4REFBQ0s7O2tDQUNDLDhEQUFDSiwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2hlbGwtYXBwLy4vc3JjL3BhZ2VzL19kb2N1bWVudC50c3g/MTg4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxuICAgICAgPEhlYWQ+XG4gICAgICAgIHsvKiBBZGQgYW55IGdsb2JhbCBoZWFkIGVsZW1lbnRzIGhlcmUgKi99XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/about.tsx":
/*!*****************************!*\
  !*** ./src/pages/about.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n\n\n\nconst AboutPage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"page-header\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"page-title\",\n                        children: \"About This Demo\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"page-subtitle\",\n                        children: \"Learn about the microfrontend architecture and implementation details\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83C\\uDFD7️ Architecture Overview\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This demonstration showcases a modern microfrontend architecture using webpack Module Federation and web components. The system consists of two main applications:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(300px, 1fr))\",\n                            gap: \"2rem\",\n                            marginTop: \"2rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"1.5rem\",\n                                    background: \"#e3f2fd\",\n                                    borderRadius: \"8px\",\n                                    border: \"1px solid #2196f3\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"\\uD83C\\uDFE0 Shell Application\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            marginTop: \"1rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Framework:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Next.js 14\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 35,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Port:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 36,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" 3000\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 36,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Role:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 37,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Host/Container\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Responsibilities:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Navigation, layout, microfrontend orchestration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    padding: \"1.5rem\",\n                                    background: \"#f3e5f5\",\n                                    borderRadius: \"8px\",\n                                    border: \"1px solid #9c27b0\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"\\uD83D\\uDED2 Checkout Microfrontend\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            marginTop: \"1rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Framework:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 50,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" React 18\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 50,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Port:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 51,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" 3001\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 51,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Role:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Remote/Microfrontend\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"Responsibilities:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    \" Checkout flow, payment processing\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDD27 Technical Implementation\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"Module Federation Configuration\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The checkout microfrontend exposes its components through webpack Module Federation:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#f8f9fa\",\n                            padding: \"1rem\",\n                            borderRadius: \"4px\",\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\",\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"// webpack.config.js (Checkout)\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"new ModuleFederationPlugin(\",\n                                    `{`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\xa0\\xa0name: 'checkout',\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\xa0\\xa0filename: 'remoteEntry.js',\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\xa0\\xa0exposes: \",\n                                    `{`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\xa0\\xa0\\xa0\\xa0'./CheckoutApp': './src/CheckoutApp',\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"\\xa0\\xa0\\xa0\\xa0'./CheckoutWebComponent': './src/CheckoutWebComponent'\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\xa0\\xa0\",\n                                    `}`\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    `}`,\n                                    \")\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        style: {\n                            marginTop: \"2rem\"\n                        },\n                        children: \"Web Component Integration\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"The shell application loads the microfrontend and uses it as a web component:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            background: \"#f8f9fa\",\n                            padding: \"1rem\",\n                            borderRadius: \"4px\",\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\",\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"// Load the remote entry\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: '<script src=\"http://localhost:3001/remoteEntry.js\"></script>'\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"// Use as web component\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"<checkout-widget></checkout-widget>\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"✨ Key Features\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(250px, 1fr))\",\n                            gap: \"1.5rem\",\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDD04 Dynamic Loading\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Microfrontends are loaded on-demand with proper error handling and fallbacks.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83C\\uDFA8 Style Isolation\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Each microfrontend maintains its own styling without conflicts.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDCF1 Responsive Design\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"All components work seamlessly across different screen sizes.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDD27 Framework Agnostic\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Web components can be used in any framework or vanilla JavaScript.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"⚡ Performance\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Code splitting and lazy loading ensure optimal performance.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDEE1️ Error Boundaries\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Robust error handling prevents cascading failures.\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 105,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDE80 Benefits of This Architecture\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                children: \"For Development Teams:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"0.5rem\",\n                                    paddingLeft: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Independent development and deployment\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Technology diversity (different frameworks per team)\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Reduced coordination overhead\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Faster development cycles\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    marginTop: \"1.5rem\"\n                                },\n                                children: \"For Business:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"0.5rem\",\n                                    paddingLeft: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Faster time to market\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Better scalability\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Reduced risk of system-wide failures\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Easier A/B testing and feature rollouts\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 163,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                style: {\n                                    marginTop: \"1.5rem\"\n                                },\n                                children: \"For Users:\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                style: {\n                                    marginTop: \"0.5rem\",\n                                    paddingLeft: \"1.5rem\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Faster page loads through code splitting\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"More reliable experience (isolated failures)\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"Consistent UI across different sections\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        children: \"\\uD83D\\uDCDA Learn More\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"This demo is based on modern microfrontend patterns and best practices. To learn more about the technologies used:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: \"grid\",\n                            gridTemplateColumns: \"repeat(auto-fit, minmax(200px, 1fr))\",\n                            gap: \"1rem\",\n                            marginTop: \"1rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDCD6 Resources:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            marginTop: \"0.5rem\",\n                                            fontSize: \"0.9rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Webpack Module Federation\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Web Components API\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Next.js Documentation\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"React 18 Features\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"\\uD83D\\uDEE0️ Tools Used:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        style: {\n                                            marginTop: \"0.5rem\",\n                                            fontSize: \"0.9rem\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"TypeScript\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Webpack 5\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"Babel\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: \"CSS Modules\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/about.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AboutPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/about.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fabout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fabout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();