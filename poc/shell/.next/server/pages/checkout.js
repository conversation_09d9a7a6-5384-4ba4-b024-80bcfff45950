/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/checkout";
exports.ids = ["pages/checkout"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fcheckout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fcheckout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./src/pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/checkout.tsx */ \"./src/pages/checkout.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/checkout\",\n        pathname: \"/checkout\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_checkout_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fcheckout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nconst Layout = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const isActive = (path)=>{\n        return router.pathname === path;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            minHeight: \"100vh\",\n            display: \"flex\",\n            flexDirection: \"column\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"header-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/\",\n                                className: \"logo\",\n                                children: \"\\uD83C\\uDFEA Shell App\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"nav\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/\",\n                                        className: isActive(\"/\") ? \"active\" : \"\",\n                                        children: \"Home\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/checkout\",\n                                        className: isActive(\"/checkout\") ? \"active\" : \"\",\n                                        children: \"Checkout\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: \"/about\",\n                                        className: isActive(\"/about\") ? \"active\" : \"\",\n                                        children: \"About\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"main-content\",\n                style: {\n                    flex: 1\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"footer\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"\\xa9 2024 Shell App - Microfrontend Demo | Built with Next.js and Module Federation\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n                lineNumber: 57,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/Layout.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvY29tcG9uZW50cy9MYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBMEI7QUFDRztBQUNXO0FBTXhDLE1BQU1HLFNBQWdDLENBQUMsRUFBRUMsUUFBUSxFQUFFO0lBQ2pELE1BQU1DLFNBQVNILHNEQUFTQTtJQUV4QixNQUFNSSxXQUFXLENBQUNDO1FBQ2hCLE9BQU9GLE9BQU9HLFFBQVEsS0FBS0Q7SUFDN0I7SUFFQSxxQkFDRSw4REFBQ0U7UUFBSUMsT0FBTztZQUFFQyxXQUFXO1lBQVNDLFNBQVM7WUFBUUMsZUFBZTtRQUFTOzswQkFFekUsOERBQUNDO2dCQUFPQyxXQUFVOzBCQUNoQiw0RUFBQ047b0JBQUlNLFdBQVU7OEJBQ2IsNEVBQUNOO3dCQUFJTSxXQUFVOzswQ0FDYiw4REFBQ2Qsa0RBQUlBO2dDQUFDZSxNQUFLO2dDQUFJRCxXQUFVOzBDQUFPOzs7Ozs7MENBR2hDLDhEQUFDRTtnQ0FBSUYsV0FBVTs7a0RBQ2IsOERBQUNkLGtEQUFJQTt3Q0FDSGUsTUFBSzt3Q0FDTEQsV0FBV1QsU0FBUyxPQUFPLFdBQVc7a0RBQ3ZDOzs7Ozs7a0RBR0QsOERBQUNMLGtEQUFJQTt3Q0FDSGUsTUFBSzt3Q0FDTEQsV0FBV1QsU0FBUyxlQUFlLFdBQVc7a0RBQy9DOzs7Ozs7a0RBR0QsOERBQUNMLGtEQUFJQTt3Q0FDSGUsTUFBSzt3Q0FDTEQsV0FBV1QsU0FBUyxZQUFZLFdBQVc7a0RBQzVDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVNULDhEQUFDWTtnQkFBS0gsV0FBVTtnQkFBZUwsT0FBTztvQkFBRVMsTUFBTTtnQkFBRTswQkFDOUMsNEVBQUNWO29CQUFJTSxXQUFVOzhCQUNaWDs7Ozs7Ozs7Ozs7MEJBS0wsOERBQUNnQjtnQkFBT0wsV0FBVTswQkFDaEIsNEVBQUNOO29CQUFJTSxXQUFVOzhCQUNiLDRFQUFDTTtrQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVFiO0FBRUEsaUVBQWVsQixNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2hlbGwtYXBwLy4vc3JjL2NvbXBvbmVudHMvTGF5b3V0LnRzeD9kZThiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgTGluayBmcm9tICduZXh0L2xpbmsnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuXG5pbnRlcmZhY2UgTGF5b3V0UHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5jb25zdCBMYXlvdXQ6IFJlYWN0LkZDPExheW91dFByb3BzPiA9ICh7IGNoaWxkcmVuIH0pID0+IHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgY29uc3QgaXNBY3RpdmUgPSAocGF0aDogc3RyaW5nKSA9PiB7XG4gICAgcmV0dXJuIHJvdXRlci5wYXRobmFtZSA9PT0gcGF0aDtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgc3R5bGU9e3sgbWluSGVpZ2h0OiAnMTAwdmgnLCBkaXNwbGF5OiAnZmxleCcsIGZsZXhEaXJlY3Rpb246ICdjb2x1bW4nIH19PlxuICAgICAgey8qIEhlYWRlciAqL31cbiAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiaGVhZGVyXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoZWFkZXItY29udGVudFwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJsb2dvXCI+XG4gICAgICAgICAgICAgIPCfj6ogU2hlbGwgQXBwXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8bmF2IGNsYXNzTmFtZT1cIm5hdlwiPlxuICAgICAgICAgICAgICA8TGluayBcbiAgICAgICAgICAgICAgICBocmVmPVwiL1wiIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17aXNBY3RpdmUoJy8nKSA/ICdhY3RpdmUnIDogJyd9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICBIb21lXG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9jaGVja291dFwiIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17aXNBY3RpdmUoJy9jaGVja291dCcpID8gJ2FjdGl2ZScgOiAnJ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIENoZWNrb3V0XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPExpbmsgXG4gICAgICAgICAgICAgICAgaHJlZj1cIi9hYm91dFwiIFxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17aXNBY3RpdmUoJy9hYm91dCcpID8gJ2FjdGl2ZScgOiAnJ31cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIEFib3V0XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvbmF2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvaGVhZGVyPlxuXG4gICAgICB7LyogTWFpbiBDb250ZW50ICovfVxuICAgICAgPG1haW4gY2xhc3NOYW1lPVwibWFpbi1jb250ZW50XCIgc3R5bGU9e3sgZmxleDogMSB9fT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9tYWluPlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJmb290ZXJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWluZXJcIj5cbiAgICAgICAgICA8cD5cbiAgICAgICAgICAgIMKpIDIwMjQgU2hlbGwgQXBwIC0gTWljcm9mcm9udGVuZCBEZW1vIHwgXG4gICAgICAgICAgICBCdWlsdCB3aXRoIE5leHQuanMgYW5kIE1vZHVsZSBGZWRlcmF0aW9uXG4gICAgICAgICAgPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTGF5b3V0O1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGluayIsInVzZVJvdXRlciIsIkxheW91dCIsImNoaWxkcmVuIiwicm91dGVyIiwiaXNBY3RpdmUiLCJwYXRoIiwicGF0aG5hbWUiLCJkaXYiLCJzdHlsZSIsIm1pbkhlaWdodCIsImRpc3BsYXkiLCJmbGV4RGlyZWN0aW9uIiwiaGVhZGVyIiwiY2xhc3NOYW1lIiwiaHJlZiIsIm5hdiIsIm1haW4iLCJmbGV4IiwiZm9vdGVyIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/components/Layout.tsx\n");

/***/ }),

/***/ "./src/components/MicrofrontendWrapper.tsx":
/*!*************************************************!*\
  !*** ./src/components/MicrofrontendWrapper.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useMicrofrontend */ \"./src/hooks/useMicrofrontend.ts\");\n\n\n\nconst MicrofrontendWrapper = ({ name, url, elementName, fallback, errorFallback, onLoad, onError, className = \"\", style = {}, ...webComponentProps })=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { isLoading, isLoaded, error, loadMicrofrontend } = (0,_hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_2__.useMicrofrontend)({\n        name,\n        url,\n        elementName\n    });\n    // Load the microfrontend when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadMicrofrontend();\n    }, []);\n    // Call callbacks when state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && onLoad) {\n            onLoad();\n        }\n    }, [\n        isLoaded,\n        onLoad\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (error && onError) {\n            onError(error);\n        }\n    }, [\n        error,\n        onError\n    ]);\n    // Render the web component when loaded\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isLoaded && containerRef.current) {\n            // Check if the web component is already in the container\n            const existingElement = containerRef.current.querySelector(elementName);\n            if (!existingElement) {\n                // Create the web component element\n                const webComponent = document.createElement(elementName);\n                // Set attributes from props\n                Object.entries(webComponentProps).forEach(([key, value])=>{\n                    if (value !== undefined && value !== null) {\n                        webComponent.setAttribute(key, String(value));\n                    }\n                });\n                // Add to container\n                containerRef.current.appendChild(webComponent);\n                console.log(`✅ Web component ${elementName} rendered successfully`);\n            }\n        }\n    }, [\n        isLoaded,\n        elementName,\n        webComponentProps\n    ]);\n    // Default fallbacks\n    const defaultFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                \"Loading \",\n                name,\n                \" microfrontend...\"\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, undefined);\n    const defaultErrorFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-error\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                children: [\n                    \"⚠️ Failed to load \",\n                    name\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 88,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: error\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: [\n                    \"Make sure the \",\n                    name,\n                    \" microfrontend is running on\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: url.replace(\"/remoteEntry.js\", \"\")\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn btn-secondary\",\n                onClick: loadMicrofrontend,\n                style: {\n                    marginTop: \"1rem\"\n                },\n                children: \"Retry\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 87,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `microfrontend-container ${className}`,\n        style: style,\n        children: error ? errorFallback || defaultErrorFallback : isLoading ? fallback || defaultFallback : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            ref: containerRef,\n            style: {\n                width: \"100%\"\n            }\n        }, void 0, false, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n            lineNumber: 114,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/components/MicrofrontendWrapper.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MicrofrontendWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/MicrofrontendWrapper.tsx\n");

/***/ }),

/***/ "./src/hooks/useMicrofrontend.ts":
/*!***************************************!*\
  !*** ./src/hooks/useMicrofrontend.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MICROFRONTEND_CONFIGS: () => (/* binding */ MICROFRONTEND_CONFIGS),\n/* harmony export */   useMicrofrontend: () => (/* binding */ useMicrofrontend)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nconst useMicrofrontend = (config)=>{\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [isLoaded, setIsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const loadMicrofrontend = async ()=>{\n        if (isLoaded || isLoading) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            // Check if the script is already loaded\n            const existingScript = document.querySelector(`script[src=\"${config.url}\"]`);\n            if (existingScript) {\n                setIsLoaded(true);\n                setIsLoading(false);\n                return;\n            }\n            // Create and load the script\n            const script = document.createElement(\"script\");\n            script.src = config.url;\n            script.type = \"text/javascript\";\n            script.async = true;\n            // Handle script load success\n            script.onload = ()=>{\n                console.log(`✅ Microfrontend ${config.name} loaded successfully`);\n                // If it's a web component, wait for it to be defined\n                if (config.elementName) {\n                    customElements.whenDefined(config.elementName).then(()=>{\n                        console.log(`✅ Web component ${config.elementName} is ready`);\n                        setIsLoaded(true);\n                        setIsLoading(false);\n                    }).catch((err)=>{\n                        console.error(`❌ Error waiting for web component ${config.elementName}:`, err);\n                        setError(`Failed to initialize web component: ${config.elementName}`);\n                        setIsLoading(false);\n                    });\n                } else {\n                    setIsLoaded(true);\n                    setIsLoading(false);\n                }\n            };\n            // Handle script load error\n            script.onerror = ()=>{\n                const errorMsg = `Failed to load microfrontend from ${config.url}`;\n                console.error(`❌ ${errorMsg}`);\n                setError(errorMsg);\n                setIsLoading(false);\n            };\n            // Add script to document\n            document.head.appendChild(script);\n        } catch (err) {\n            const errorMsg = `Error loading microfrontend: ${err}`;\n            console.error(`❌ ${errorMsg}`);\n            setError(errorMsg);\n            setIsLoading(false);\n        }\n    };\n    return {\n        isLoading,\n        isLoaded,\n        error,\n        loadMicrofrontend\n    };\n};\n// Predefined configurations for known microfrontends\nconst MICROFRONTEND_CONFIGS = {\n    checkout: {\n        name: \"checkout\",\n        url: \"http://localhost:3001/remoteEntry.js\",\n        elementName: \"checkout-widget\"\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useMicrofrontend.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_1___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"Shell App - Microfrontend Demo\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: \"Next.js shell application demonstrating microfrontend integration\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width, initial-scale=1\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        href: \"/favicon.ico\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_app.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDNkI7QUFDQztBQUVmLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0U7OzBCQUNFLDhEQUFDSCxrREFBSUE7O2tDQUNILDhEQUFDSTtrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsTUFBSzt3QkFBY0MsU0FBUTs7Ozs7O2tDQUNqQyw4REFBQ0Y7d0JBQUtDLE1BQUs7d0JBQVdDLFNBQVE7Ozs7OztrQ0FDOUIsOERBQUNDO3dCQUFLQyxLQUFJO3dCQUFPQyxNQUFLOzs7Ozs7Ozs7Ozs7MEJBRXhCLDhEQUFDUjtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7OztBQUc5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3NoZWxsLWFwcC8uL3NyYy9wYWdlcy9fYXBwLnRzeD9mOWQ2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgQXBwUHJvcHMgfSBmcm9tICduZXh0L2FwcCc7XG5pbXBvcnQgSGVhZCBmcm9tICduZXh0L2hlYWQnO1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPD5cbiAgICAgIDxIZWFkPlxuICAgICAgICA8dGl0bGU+U2hlbGwgQXBwIC0gTWljcm9mcm9udGVuZCBEZW1vPC90aXRsZT5cbiAgICAgICAgPG1ldGEgbmFtZT1cImRlc2NyaXB0aW9uXCIgY29udGVudD1cIk5leHQuanMgc2hlbGwgYXBwbGljYXRpb24gZGVtb25zdHJhdGluZyBtaWNyb2Zyb250ZW5kIGludGVncmF0aW9uXCIgLz5cbiAgICAgICAgPG1ldGEgbmFtZT1cInZpZXdwb3J0XCIgY29udGVudD1cIndpZHRoPWRldmljZS13aWR0aCwgaW5pdGlhbC1zY2FsZT0xXCIgLz5cbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIGhyZWY9XCIvZmF2aWNvbi5pY29cIiAvPlxuICAgICAgPC9IZWFkPlxuICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgIDwvPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkhlYWQiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJ0aXRsZSIsIm1ldGEiLCJuYW1lIiwiY29udGVudCIsImxpbmsiLCJyZWwiLCJocmVmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/_document.tsx":
/*!*********************************!*\
  !*** ./src/pages/_document.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                        lineNumber: 10,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2RvY3VtZW50LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOzs7OzswQkFHTCw4REFBQ0s7O2tDQUNDLDhEQUFDSiwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2hlbGwtYXBwLy4vc3JjL3BhZ2VzL19kb2N1bWVudC50c3g/MTg4ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSAnbmV4dC9kb2N1bWVudCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxuICAgICAgPEhlYWQ+XG4gICAgICAgIHsvKiBBZGQgYW55IGdsb2JhbCBoZWFkIGVsZW1lbnRzIGhlcmUgKi99XG4gICAgICA8L0hlYWQ+XG4gICAgICA8Ym9keT5cbiAgICAgICAgPE1haW4gLz5cbiAgICAgICAgPE5leHRTY3JpcHQgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L0h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSHRtbCIsIkhlYWQiLCJNYWluIiwiTmV4dFNjcmlwdCIsIkRvY3VtZW50IiwibGFuZyIsImJvZHkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/_document.tsx\n");

/***/ }),

/***/ "./src/pages/checkout.tsx":
/*!********************************!*\
  !*** ./src/pages/checkout.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"./src/components/Layout.tsx\");\n/* harmony import */ var _components_MicrofrontendWrapper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/MicrofrontendWrapper */ \"./src/components/MicrofrontendWrapper.tsx\");\n/* harmony import */ var _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useMicrofrontend */ \"./src/hooks/useMicrofrontend.ts\");\n\n\n\n\n\nconst CheckoutPage = ()=>{\n    const [loadStatus, setLoadStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"loading\");\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleMicrofrontendLoad = ()=>{\n        console.log(\"✅ Checkout microfrontend loaded successfully\");\n        setLoadStatus(\"loaded\");\n    };\n    const handleMicrofrontendError = (error)=>{\n        console.error(\"❌ Checkout microfrontend failed to load:\", error);\n        setLoadStatus(\"error\");\n        setErrorMessage(error);\n    };\n    const customLoadingFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-loading\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                textAlign: \"center\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"\\uD83D\\uDED2 Loading Checkout...\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Connecting to checkout microfrontend service\"\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginTop: \"1rem\",\n                        fontSize: \"0.9rem\",\n                        color: \"#666\",\n                        fontFamily: \"monospace\"\n                    },\n                    children: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, undefined);\n    const customErrorFallback = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"microfrontend-error\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                children: \"⚠️ Checkout Service Unavailable\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"We're having trouble loading the checkout experience.\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    background: \"#fff\",\n                    padding: \"1rem\",\n                    borderRadius: \"4px\",\n                    margin: \"1rem 0\",\n                    border: \"1px solid #f56565\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Error:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    \" \",\n                    errorMessage\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                style: {\n                    marginBottom: \"1rem\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Troubleshooting:\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        style: {\n                            textAlign: \"left\",\n                            marginTop: \"0.5rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: \"Make sure the checkout service is running on port 3001\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Run: \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"cd poc/checkout && npm start\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 20\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    \"Check that \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        children: \"http://localhost:3001\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 26\n                                    }, undefined),\n                                    \" is accessible\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                className: \"btn\",\n                onClick: ()=>window.location.reload(),\n                style: {\n                    background: \"#007bff\",\n                    color: \"white\"\n                },\n                children: \"\\uD83D\\uDD04 Retry Loading\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MicrofrontendWrapper__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    name: \"checkout\",\n                    url: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url,\n                    elementName: _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.elementName,\n                    onLoad: handleMicrofrontendLoad,\n                    onError: handleMicrofrontendError,\n                    fallback: customLoadingFallback,\n                    errorFallback: customErrorFallback\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"card\",\n                style: {\n                    marginTop: \"2rem\",\n                    background: \"#f8f9fa\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        children: \"\\uD83D\\uDC1B Debug Information\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            fontFamily: \"monospace\",\n                            fontSize: \"0.9rem\"\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Load Status:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    loadStatus\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Service URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.url\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Element Name:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 18\n                                    }, undefined),\n                                    \" \",\n                                    _hooks_useMicrofrontend__WEBPACK_IMPORTED_MODULE_4__.MICROFRONTEND_CONFIGS.checkout.elementName\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            errorMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 35\n                                    }, undefined),\n                                    \" \",\n                                    errorMessage\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 30\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n                lineNumber: 90,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/projects/private/arch-proposal-ibm/poc/shell/src/pages/checkout.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CheckoutPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/checkout.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcheckout&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fcheckout.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();