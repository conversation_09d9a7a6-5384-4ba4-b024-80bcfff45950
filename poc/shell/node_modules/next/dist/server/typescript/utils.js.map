{"version": 3, "sources": ["../../../src/server/typescript/utils.ts"], "names": ["getEntryInfo", "getInfo", "getSource", "getTs", "getType<PERSON><PERSON>cker", "init", "isAppEntryFile", "isDefaultFunctionExport", "isInsideApp", "isPageFile", "isPositionInsideNode", "log", "removeStringQuotes", "ts", "info", "appDirRegExp", "message", "project", "projectService", "logger", "opts", "projectDir", "getCurrentDirectory", "RegExp", "replace", "languageService", "getProgram", "fileName", "getSourceFile", "str", "position", "node", "start", "getFullStart", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "isFunctionDeclaration", "hasExportKeyword", "hasDefaultKeyword", "modifiers", "modifier", "kind", "SyntaxKind", "ExportKeyword", "DefaultKeyword", "filePath", "test", "path", "basename", "throwOnInvalidDirective", "source", "isDirective", "isClientEntry", "isServerEntry", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "isExpressionStatement", "isStringLiteral", "expression", "text", "e", "messageText", "getStart", "length", "getWidth", "client", "server"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;IA8FgBA,YAAY;eAAZA;;IA/DAC,OAAO;eAAPA;;IAQAC,SAAS;eAATA;;IAZAC,KAAK;eAALA;;IAQAC,cAAc;eAAdA;;IArBAC,IAAI;eAAJA;;IAkEHC,cAAc;eAAdA;;IA5BAC,uBAAuB;eAAvBA;;IAyBAC,WAAW;eAAXA;;IASAC,UAAU;eAAVA;;IAvCAC,oBAAoB;eAApBA;;IAtCGC,GAAG;eAAHA;;IAkCAC,kBAAkB;eAAlBA;;;6DA3CC;;;;;;AAKjB,IAAIC;AACJ,IAAIC;AACJ,IAAIC;AAEG,SAASJ,IAAIK,OAAe;IACjCF,KAAKG,OAAO,CAACC,cAAc,CAACC,MAAM,CAACL,IAAI,CAACE;AAC1C;AAGO,SAASX,KAAKe,IAGpB;IACCP,KAAKO,KAAKP,EAAE;IACZC,OAAOM,KAAKN,IAAI;IAChB,MAAMO,aAAaP,KAAKG,OAAO,CAACK,mBAAmB;IACnDP,eAAe,IAAIQ,OACjB,MAAM,AAACF,CAAAA,aAAa,aAAY,EAAGG,OAAO,CAAC,UAAU;IAEvDb,IAAI,yCAAyCU;AAC/C;AAEO,SAASlB;IACd,OAAOU;AACT;AAEO,SAASZ;IACd,OAAOa;AACT;AAEO,SAASV;QACPU;IAAP,QAAOA,mCAAAA,KAAKW,eAAe,CAACC,UAAU,uBAA/BZ,iCAAmCV,cAAc;AAC1D;AAEO,SAASF,UAAUyB,QAAgB;QACjCb;IAAP,QAAOA,mCAAAA,KAAKW,eAAe,CAACC,UAAU,uBAA/BZ,iCAAmCc,aAAa,CAACD;AAC1D;AAEO,SAASf,mBAAmBiB,GAAW;IAC5C,OAAOA,IAAIL,OAAO,CAAC,kBAAkB;AACvC;AAEO,MAAMd,uBAAuB,CAACoB,UAAkBC;IACrD,MAAMC,QAAQD,KAAKE,YAAY;IAC/B,OAAOD,SAASF,YAAYA,YAAYC,KAAKG,YAAY,KAAKF;AAChE;AAEO,MAAMzB,0BAA0B,CACrCwB;IAEA,IAAIlB,GAAGsB,qBAAqB,CAACJ,OAAO;QAClC,IAAIK,mBAAmB;QACvB,IAAIC,oBAAoB;QAExB,IAAIN,KAAKO,SAAS,EAAE;YAClB,KAAK,MAAMC,YAAYR,KAAKO,SAAS,CAAE;gBACrC,IAAIC,SAASC,IAAI,KAAK3B,GAAG4B,UAAU,CAACC,aAAa,EAAE;oBACjDN,mBAAmB;gBACrB,OAAO,IAAIG,SAASC,IAAI,KAAK3B,GAAG4B,UAAU,CAACE,cAAc,EAAE;oBACzDN,oBAAoB;gBACtB;YACF;QACF;QAEA,4BAA4B;QAC5B,IAAID,oBAAoBC,mBAAmB;YACzC,OAAO;QACT;IACF;IACA,OAAO;AACT;AAEO,MAAM7B,cAAc,CAACoC;IAC1B,OAAO7B,aAAa8B,IAAI,CAACD;AAC3B;AACO,MAAMtC,iBAAiB,CAACsC;IAC7B,OACE7B,aAAa8B,IAAI,CAACD,aAClB,uCAAuCC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAE9D;AACO,MAAMnC,aAAa,CAACmC;IACzB,OACE7B,aAAa8B,IAAI,CAACD,aAClB,8BAA8BC,IAAI,CAACC,aAAI,CAACC,QAAQ,CAACH;AAErD;AAGO,SAAS5C,aACd2B,QAAgB,EAChBqB,uBAAiC;IAEjC,MAAMC,SAAS/C,UAAUyB;IACzB,IAAIsB,QAAQ;QACV,IAAIC,cAAc;QAClB,IAAIC,gBAAgB;QACpB,IAAIC,gBAAgB;QAEpBvC,GAAGwC,YAAY,CAACJ,QAAS,CAAClB;YACxB,IACElB,GAAGyC,qBAAqB,CAACvB,SACzBlB,GAAG0C,eAAe,CAACxB,KAAKyB,UAAU,GAClC;gBACA,IAAIzB,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBACzC,IAAIP,aAAa;wBACfC,gBAAgB;oBAClB,OAAO;wBACL,IAAIH,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF,OAAO,IAAI3B,KAAKyB,UAAU,CAACC,IAAI,KAAK,cAAc;oBAChD,IAAIP,aAAa;wBACfE,gBAAgB;oBAClB,OAAO;wBACL,IAAIJ,yBAAyB;4BAC3B,MAAMU,IAAI;gCACRC,aACE;gCACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;gCAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;4BAClC;4BACA,MAAMJ;wBACR;oBACF;gBACF;gBAEA,IAAIP,iBAAiBC,eAAe;oBAClC,MAAMM,IAAI;wBACRC,aACE;wBACF3B,OAAOD,KAAKyB,UAAU,CAACI,QAAQ;wBAC/BC,QAAQ9B,KAAKyB,UAAU,CAACM,QAAQ;oBAClC;oBACA,MAAMJ;gBACR;YACF,OAAO;gBACLR,cAAc;YAChB;QACF;QAEA,OAAO;YAAEa,QAAQZ;YAAea,QAAQZ;QAAc;IACxD;IAEA,OAAO;QAAEW,QAAQ;QAAOC,QAAQ;IAAM;AACxC"}